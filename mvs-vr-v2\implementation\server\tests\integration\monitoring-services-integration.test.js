/**
 * Integration Tests for New Monitoring Services
 *
 * Tests the integration between rate limit monitoring, enhanced performance monitoring,
 * and other monitoring services
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// Mock the monitoring services
const mockRateLimitMonitor = {
  track: vi.fn(),
  getMetrics: vi.fn(),
  getTopIPs: vi.fn(),
  destroy: vi.fn(),
};

const mockPerformanceMonitor = {
  start: vi.fn(),
  stop: vi.fn(),
  recordMetric: vi.fn(),
  getPerformanceSummary: vi.fn(),
  acknowledgeAlert: vi.fn(),
};

// Mock Redis
const mockRedis = {
  get: vi.fn().mockResolvedValue(null),
  set: vi.fn().mockResolvedValue('OK'),
  del: vi.fn().mockResolvedValue(1),
  incr: vi.fn().mockResolvedValue(1),
  expire: vi.fn().mockResolvedValue(1),
  script: vi.fn().mockResolvedValue('sha1-hash'),
  eval: vi.fn().mockResolvedValue([1, 60000]),
  evalsha: vi.fn().mockResolvedValue([1, 60000]),
  sendCommand: vi.fn().mockImplementation((command, ...args) => {
    switch (command) {
      case 'SCRIPT':
        return Promise.resolve('sha1-hash');
      case 'EVALSHA':
        return Promise.resolve([1, 60000]);
      default:
        return Promise.resolve('OK');
    }
  }),
};

describe('Monitoring Services Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rate Limit Monitor Integration', () => {
    it('should track rate limit events and provide metrics', async () => {
      // Setup
      const rateLimitEvent = {
        ip: '*************',
        endpoint: '/api/vendors',
        method: 'GET',
        timestamp: Date.now(),
        reason: 'Too many requests',
      };

      // Mock the tracking
      mockRateLimitMonitor.track.mockImplementation(event => {
        expect(event).toEqual(rateLimitEvent);
      });

      mockRateLimitMonitor.getMetrics.mockReturnValue({
        totalEvents: 1,
        hourlyCount: 1,
        topEndpoints: [{ endpoint: '/api/vendors', count: 1 }],
        topIPs: [{ ip: '*************', events: 1, endpoints: 1 }],
        timestamp: Date.now(),
      });

      // Execute
      mockRateLimitMonitor.track(rateLimitEvent);
      const metrics = mockRateLimitMonitor.getMetrics();

      // Verify
      expect(mockRateLimitMonitor.track).toHaveBeenCalledWith(rateLimitEvent);
      expect(metrics.totalEvents).toBe(1);
      expect(metrics.topEndpoints).toHaveLength(1);
      expect(metrics.topIPs).toHaveLength(1);
    });

    it('should identify top rate limited IPs', async () => {
      // Setup
      const mockTopIPs = [
        { ip: '*************', events: 10, endpoints: ['api/vendors'], lastSeen: Date.now() },
        { ip: '*************', events: 5, endpoints: ['api/assets'], lastSeen: Date.now() },
      ];

      mockRateLimitMonitor.getTopIPs.mockReturnValue(mockTopIPs);

      // Execute
      const topIPs = mockRateLimitMonitor.getTopIPs(10);

      // Verify
      expect(topIPs).toHaveLength(2);
      expect(topIPs[0].events).toBe(10);
      expect(topIPs[1].events).toBe(5);
    });
  });

  describe('Enhanced Performance Monitor Integration', () => {
    it('should start and stop monitoring', async () => {
      // Execute
      mockPerformanceMonitor.start();
      mockPerformanceMonitor.stop();

      // Verify
      expect(mockPerformanceMonitor.start).toHaveBeenCalled();
      expect(mockPerformanceMonitor.stop).toHaveBeenCalled();
    });

    it('should record performance metrics', async () => {
      // Setup
      const metricType = 'response_time';
      const value = 150;
      const metadata = { endpoint: '/api/vendors', method: 'GET' };

      // Execute
      mockPerformanceMonitor.recordMetric(metricType, value, metadata);

      // Verify
      expect(mockPerformanceMonitor.recordMetric).toHaveBeenCalledWith(metricType, value, metadata);
    });

    it('should provide performance summary', async () => {
      // Setup
      const mockSummary = {
        timestamp: Date.now(),
        metrics: {
          response_time: { current: 150, avg: 120, min: 80, max: 200, count: 10 },
          cpu_usage: { current: 45, avg: 40, min: 30, max: 60, count: 10 },
        },
        alerts: [],
        recommendations: [],
        baselines: {},
      };

      mockPerformanceMonitor.getPerformanceSummary.mockReturnValue(mockSummary);

      // Execute
      const summary = mockPerformanceMonitor.getPerformanceSummary();

      // Verify
      expect(summary.metrics.response_time.current).toBe(150);
      expect(summary.metrics.cpu_usage.current).toBe(45);
    });
  });

  describe('Cross-Service Integration', () => {
    it('should integrate rate limiting with performance monitoring', async () => {
      // Setup - simulate a rate limit event that affects performance
      const rateLimitEvent = {
        ip: '*************',
        endpoint: '/api/vendors',
        method: 'GET',
        timestamp: Date.now(),
        reason: 'Too many requests',
      };

      // Mock rate limit tracking
      mockRateLimitMonitor.track.mockImplementation(event => {
        // When rate limit is triggered, it should also record performance impact
        mockPerformanceMonitor.recordMetric('rate_limit_triggered', 1, {
          ip: event.ip,
          endpoint: event.endpoint,
        });
      });

      // Execute
      mockRateLimitMonitor.track(rateLimitEvent);

      // Verify
      expect(mockRateLimitMonitor.track).toHaveBeenCalledWith(rateLimitEvent);
      expect(mockPerformanceMonitor.recordMetric).toHaveBeenCalledWith('rate_limit_triggered', 1, {
        ip: '*************',
        endpoint: '/api/vendors',
      });
    });

    it('should correlate performance alerts with rate limiting', async () => {
      // Setup
      const performanceAlert = {
        id: 'alert-123',
        metricType: 'response_time',
        value: 3000,
        level: 'critical',
        timestamp: Date.now(),
      };

      const rateLimitMetrics = {
        totalEvents: 50,
        hourlyCount: 30,
        topEndpoints: [{ endpoint: '/api/vendors', count: 25 }],
      };

      mockPerformanceMonitor.getPerformanceSummary.mockReturnValue({
        alerts: [performanceAlert],
        metrics: {},
        recommendations: [],
        baselines: {},
      });

      mockRateLimitMonitor.getMetrics.mockReturnValue(rateLimitMetrics);

      // Execute
      const performanceSummary = mockPerformanceMonitor.getPerformanceSummary();
      const rateLimitData = mockRateLimitMonitor.getMetrics();

      // Verify correlation
      expect(performanceSummary.alerts).toHaveLength(1);
      expect(rateLimitData.hourlyCount).toBeGreaterThan(0);

      // In a real implementation, we would correlate high rate limiting with performance issues
      const hasCorrelation = performanceSummary.alerts.length > 0 && rateLimitData.hourlyCount > 20;
      expect(hasCorrelation).toBe(true);
    });
  });

  describe('Redis Integration', () => {
    it('should handle Redis operations for monitoring data', async () => {
      // Setup
      const cacheKey = 'monitoring:metrics:response_time';
      const metricsData = JSON.stringify({ avg: 120, count: 10 });

      mockRedis.set.mockResolvedValue('OK');
      mockRedis.get.mockResolvedValue(metricsData);

      // Execute
      await mockRedis.set(cacheKey, metricsData, 'EX', 300);
      const cachedData = await mockRedis.get(cacheKey);

      // Verify
      expect(mockRedis.set).toHaveBeenCalledWith(cacheKey, metricsData, 'EX', 300);
      expect(mockRedis.get).toHaveBeenCalledWith(cacheKey);
      expect(cachedData).toBe(metricsData);
    });

    it('should handle Redis script operations for rate limiting', async () => {
      // Setup
      const scriptSha = 'sha1-rate-limit-script';
      const rateLimitKey = 'rate_limit:*************:/api/vendors';

      mockRedis.script.mockResolvedValue(scriptSha);
      mockRedis.evalsha.mockResolvedValue([1, 60000]); // [current_count, ttl_ms]

      // Execute
      const sha = await mockRedis.script('LOAD', 'rate_limit_script_content');
      const result = await mockRedis.evalsha(sha, 1, rateLimitKey, 10, 60);

      // Verify
      expect(mockRedis.script).toHaveBeenCalledWith('LOAD', 'rate_limit_script_content');
      expect(mockRedis.evalsha).toHaveBeenCalledWith(sha, 1, rateLimitKey, 10, 60);
      expect(result).toEqual([1, 60000]);
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle monitoring service failures gracefully', async () => {
      // Setup - simulate service failure
      mockRateLimitMonitor.track.mockImplementation(() => {
        throw new Error('Rate limit monitor unavailable');
      });

      // Execute with error handling
      let errorCaught = false;
      try {
        mockRateLimitMonitor.track({ ip: '*************' });
      } catch (error) {
        errorCaught = true;
        expect(error.message).toBe('Rate limit monitor unavailable');
      }

      // Verify
      expect(errorCaught).toBe(true);
    });

    it('should handle Redis connection failures', async () => {
      // Setup - simulate Redis failure
      mockRedis.get.mockRejectedValue(new Error('Redis connection failed'));

      // Execute with error handling
      let errorCaught = false;
      try {
        await mockRedis.get('test-key');
      } catch (error) {
        errorCaught = true;
        expect(error.message).toBe('Redis connection failed');
      }

      // Verify
      expect(errorCaught).toBe(true);
    });
  });

  describe('Performance Under Load', () => {
    it('should handle concurrent monitoring operations', async () => {
      // Reset the mock to normal behavior for this test
      mockRateLimitMonitor.track.mockImplementation(() => {
        return Promise.resolve({ success: true });
      });

      // Setup
      const concurrentOperations = 100;
      const operations = [];

      // Create concurrent operations
      for (let i = 0; i < concurrentOperations; i++) {
        operations.push(
          Promise.resolve().then(() => {
            mockRateLimitMonitor.track({
              ip: `192.168.1.${i}`,
              endpoint: '/api/test',
              method: 'GET',
              timestamp: Date.now(),
            });
          }),
        );
      }

      // Execute
      await Promise.all(operations);

      // Verify
      expect(mockRateLimitMonitor.track).toHaveBeenCalledTimes(concurrentOperations);
    });

    it('should maintain performance with high metric volume', async () => {
      // Setup
      const metricCount = 1000;
      const startTime = Date.now();

      // Execute
      for (let i = 0; i < metricCount; i++) {
        mockPerformanceMonitor.recordMetric('test_metric', i, { iteration: i });
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Verify performance (should complete quickly)
      expect(mockPerformanceMonitor.recordMetric).toHaveBeenCalledTimes(metricCount);
      expect(duration).toBeLessThan(1000); // Should complete in less than 1 second
    });
  });
});
