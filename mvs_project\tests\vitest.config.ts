/// <reference types="vitest" />
import { defineConfig } from 'vitest/config'
import path from 'path'

export default defineConfig({
  test: {
    environment: 'node',
    globals: true,
    setupFiles: ['./vitest.setup.ts'],
    include: ['**/*.test.ts', '**/*.spec.ts'],
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/playwright/**',
      // Exclude utility files that are not tests
      '**/tests/utils/**/*.js',
      '**/tests/utils/**/*.ts',
      '**/tests/config/**/*.js',
      '**/tests/config/**/*.ts',
      '**/tests/fixtures/**',
      '**/tests/scripts/**',
      // Exclude specific utility files
      '**/safe-command-executor.spec.ts',
      '**/safe-command-executor.test.ts',
      '**/test-config.js',
      '**/test-config.ts',
      '**/ci_test_runner.py',
      '**/utils.py',
    ],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      exclude: [
        'node_modules/**',
        'dist/**',
        'coverage/**',
        '**/*.d.ts',
        'vitest.config.ts',
        'vitest.setup.ts',
      ],
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, '../'),
      '@tests': path.resolve(__dirname, './'),
    },
  },
})
