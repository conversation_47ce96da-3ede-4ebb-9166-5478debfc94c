/**
 * Comprehensive Test Configuration
 *
 * Central configuration for all testing frameworks and environments:
 * - Unit, Integration, E2E, Load testing settings
 * - Environment-specific configurations
 * - Performance thresholds
 * - Monitoring and alerting settings
 */

const path = require('path');

// Base configuration
const baseConfig = {
  // Test timeouts
  timeouts: {
    unit: 10000, // 10 seconds
    integration: 30000, // 30 seconds
    e2e: 60000, // 1 minute
    load: 300000, // 5 minutes
  },

  // Retry settings
  retries: {
    unit: 2,
    integration: 3,
    e2e: 2,
    load: 1,
  },

  // Parallel execution
  parallel: {
    unit: true,
    integration: false, // Sequential for database consistency
    e2e: false, // Sequential for browser stability
    load: false, // Single instance for accurate metrics
  },

  // Coverage thresholds
  coverage: {
    global: {
      lines: 70,
      functions: 70,
      branches: 70,
      statements: 70,
    },
    api: {
      lines: 80,
      functions: 80,
      branches: 80,
      statements: 80,
    },
    services: {
      lines: 75,
      functions: 75,
      branches: 75,
      statements: 75,
    },
  },

  // Performance thresholds
  performance: {
    api: {
      maxResponseTime: 2000, // 2 seconds
      maxDbQueryTime: 1000, // 1 second
      maxMemoryUsage: 512, // 512 MB
      maxCpuUsage: 80, // 80%
    },
    load: {
      maxAvgResponseTime: 1500, // 1.5 seconds
      maxP95ResponseTime: 3000, // 3 seconds
      maxErrorRate: 0.05, // 5%
      minThroughput: 100, // 100 req/s
    },
    e2e: {
      maxPageLoadTime: 5000, // 5 seconds
      maxInteractionTime: 2000, // 2 seconds
    },
  },

  // Test data
  testData: {
    vendor: {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      company: 'Test Vendor Company',
    },
    client: {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: 'Test Client',
    },
    admin: {
      email: '<EMAIL>',
      password: 'AdminPassword123!',
    },
  },
};

// Environment-specific configurations
const environments = {
  local: {
    api: {
      baseUrl: 'http://localhost:3000',
      timeout: 30000,
    },
    directus: {
      url: 'http://localhost:8055',
      email: '<EMAIL>',
      password: 'admin',
    },
    supabase: {
      url: 'http://localhost:54321',
      anonKey:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTl9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0',
      serviceKey:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5OX0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU',
    },
    database: {
      host: 'localhost',
      port: 54322,
      database: 'postgres',
      username: 'postgres',
      password: 'postgres',
    },
    redis: {
      url: 'redis://localhost:6379',
    },
    websocket: {
      url: 'ws://localhost:3000',
    },
  },

  staging: {
    api: {
      baseUrl: 'https://staging-api.mvs.kanousai.com',
      timeout: 30000,
    },
    directus: {
      url: 'https://staging-cms.mvs.kanousai.com',
      email: process.env.STAGING_DIRECTUS_EMAIL,
      password: process.env.STAGING_DIRECTUS_PASSWORD,
    },
    supabase: {
      url: 'https://hiyqiqbgiueyyvqoqhht.supabase.co',
      anonKey: process.env.STAGING_SUPABASE_ANON_KEY,
      serviceKey: process.env.STAGING_SUPABASE_SERVICE_ROLE_KEY,
    },
    websocket: {
      url: 'wss://staging-api.mvs.kanousai.com',
    },
  },

  production: {
    api: {
      baseUrl: 'https://api.mvs.kanousai.com',
      timeout: 30000,
    },
    directus: {
      url: 'https://cms.mvs.kanousai.com',
      email: process.env.PROD_DIRECTUS_EMAIL,
      password: process.env.PROD_DIRECTUS_PASSWORD,
    },
    supabase: {
      url: process.env.PROD_SUPABASE_URL,
      anonKey: process.env.PROD_SUPABASE_ANON_KEY,
      serviceKey: process.env.PROD_SUPABASE_SERVICE_KEY,
    },
    websocket: {
      url: 'wss://api.mvs.kanousai.com',
    },
  },
};

// Test framework configurations
const frameworks = {
  vitest: {
    config: {
      globals: true,
      environment: 'jsdom',
      setupFiles: ['./tests/setup/vitest.setup.ts'],
      include: ['**/*.{test,spec,vitest}.{js,ts}', '**/tests/**/*.{js,ts}'],
      exclude: [
        '**/node_modules/**',
        '**/dist/**',
        '**/.{idea,git,cache,output,temp}/**',
        '**/tests/e2e/**',
        '**/tests/load/**',
      ],
      testTimeout: baseConfig.timeouts.unit,
      hookTimeout: 10000,
      pool: 'forks',
      poolOptions: {
        forks: {
          singleFork: true,
        },
      },
    },
  },

  playwright: {
    config: {
      testDir: './tests/e2e',
      timeout: baseConfig.timeouts.e2e,
      fullyParallel: baseConfig.parallel.e2e,
      forbidOnly: !!process.env.CI,
      retries: baseConfig.retries.e2e,
      workers: process.env.CI ? 1 : undefined,
      reporter: [
        ['html'],
        ['json', { outputFile: 'test-results/e2e-results.json' }],
        ['junit', { outputFile: 'test-results/e2e-results.xml' }],
      ],
      use: {
        trace: 'on-first-retry',
        screenshot: 'only-on-failure',
        video: 'retain-on-failure',
      },
      projects: [
        {
          name: 'chromium',
          use: { browserName: 'chromium' },
        },
        {
          name: 'firefox',
          use: { browserName: 'firefox' },
        },
        {
          name: 'webkit',
          use: { browserName: 'webkit' },
        },
      ],
    },
  },

  k6: {
    config: {
      scenarios: {
        smoke: {
          executor: 'constant-vus',
          vus: 1,
          duration: '1m',
        },
        load: {
          executor: 'ramping-vus',
          startVUs: 5,
          stages: [
            { duration: '2m', target: 20 },
            { duration: '5m', target: 20 },
            { duration: '2m', target: 0 },
          ],
        },
        stress: {
          executor: 'ramping-vus',
          startVUs: 20,
          stages: [
            { duration: '2m', target: 50 },
            { duration: '5m', target: 50 },
            { duration: '2m', target: 100 },
            { duration: '3m', target: 100 },
            { duration: '3m', target: 0 },
          ],
        },
        spike: {
          executor: 'ramping-vus',
          startVUs: 10,
          stages: [
            { duration: '1m', target: 10 },
            { duration: '30s', target: 100 },
            { duration: '1m', target: 100 },
            { duration: '30s', target: 10 },
            { duration: '1m', target: 10 },
          ],
        },
      },
      thresholds: {
        http_req_duration: ['p(95)<2000'],
        http_req_failed: ['rate<0.05'],
        http_reqs: ['rate>10'],
      },
    },
  },
};

// Monitoring and alerting configuration
const monitoring = {
  prometheus: {
    url: process.env.PROMETHEUS_URL || 'http://localhost:9090',
    pushgateway: process.env.PUSHGATEWAY_URL || 'http://localhost:9091',
  },

  grafana: {
    url: process.env.GRAFANA_URL || 'http://localhost:3001',
    apiKey: process.env.GRAFANA_API_KEY,
  },

  alerts: {
    slack: {
      webhook: process.env.SLACK_WEBHOOK_URL,
      channel: '#test-alerts',
    },
    email: {
      enabled: false,
      recipients: ['<EMAIL>'],
    },
  },

  thresholds: {
    coverageThreshold: 70,
    performanceRegressionThreshold: 20,
    failureRateThreshold: 5,
    responseTimeThreshold: 2000,
    errorRateThreshold: 0.05,
  },
};

// Export configuration based on environment
function getConfig(env = 'local') {
  const environment = environments[env] || environments.local;

  return {
    ...baseConfig,
    environment,
    frameworks,
    monitoring,
    env,

    // Helper methods
    getApiUrl: () => environment.api.baseUrl,
    getDirectusUrl: () => environment.directus.url,
    getSupabaseUrl: () => environment.supabase.url,
    getWebSocketUrl: () => environment.websocket.url,

    // Test data with environment-specific overrides
    getTestData: () => ({
      ...baseConfig.testData,
      ...environment.testData,
    }),

    // Performance thresholds with environment-specific overrides
    getPerformanceThresholds: () => ({
      ...baseConfig.performance,
      ...environment.performance,
    }),
  };
}

module.exports = {
  getConfig,
  baseConfig,
  environments,
  frameworks,
  monitoring,
};
