name: Enhanced Server CI/CD Pipeline

on:
  push:
    branches: [main, develop]
    paths:
      - 'mvs-vr-v2/implementation/server/**'
      - '.github/workflows/server-ci.yml'
  pull_request:
    branches: [main, develop]
    paths:
      - 'mvs-vr-v2/implementation/server/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      test_suite:
        description: 'Test suite to run'
        required: false
        default: 'all'
        type: choice
        options:
          - all
          - unit
          - integration
          - e2e
          - load
      skip_tests:
        description: 'Skip tests (emergency deployment)'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/mvs-vr-server
  DOCKER_REGISTRY: ghcr.io
  DOCKER_REGISTRY_USERNAME: ${{ github.actor }}
  DOCKER_REGISTRY_PASSWORD: ${{ secrets.GITHUB_TOKEN }}
  TEST_OUTPUT_DIR: ${{ github.workspace }}/test-results
  COVERAGE_OUTPUT_DIR: ${{ github.workspace }}/coverage
  PERFORMANCE_TEST_OUTPUT: ${{ github.workspace }}/performance-results

jobs:
  # Pre-flight checks
  pre-flight:
    name: Pre-flight Checks
    runs-on: ubuntu-latest
    outputs:
      should-run-tests: ${{ steps.check-conditions.outputs.should-run-tests }}
      test-suite: ${{ steps.check-conditions.outputs.test-suite }}
      environment: ${{ steps.check-conditions.outputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check conditions
        id: check-conditions
        run: |
          if [[ "${{ github.event.inputs.skip_tests }}" == "true" ]]; then
            echo "should-run-tests=false" >> $GITHUB_OUTPUT
          else
            echo "should-run-tests=true" >> $GITHUB_OUTPUT
          fi

          echo "test-suite=${{ github.event.inputs.test_suite || 'all' }}" >> $GITHUB_OUTPUT
          echo "environment=${{ github.event.inputs.environment || 'staging' }}" >> $GITHUB_OUTPUT

      - name: Validate configuration
        run: |
          cd mvs-vr-v2/implementation/server
          if [[ ! -f "package.json" ]]; then
            echo "Error: package.json not found"
            exit 1
          fi

          if [[ ! -f "vitest.config.ts" ]]; then
            echo "Error: vitest.config.ts not found"
            exit 1
          fi

  # Code quality and security
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    needs: pre-flight
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: mvs-vr-v2/implementation/server/package-lock.json

      - name: Install dependencies
        run: |
          cd mvs-vr-v2/implementation/server
          npm ci

      - name: Run ESLint
        run: |
          cd mvs-vr-v2/implementation/server
          npm run lint -- --format=json --output-file=${{ env.TEST_OUTPUT_DIR }}/eslint-report.json
        continue-on-error: true

      - name: Run Prettier check
        run: |
          cd mvs-vr-v2/implementation/server
          npm run format:check

      - name: Security audit
        run: |
          cd mvs-vr-v2/implementation/server
          npm audit --audit-level=moderate --json > ${{ env.TEST_OUTPUT_DIR }}/security-audit.json
        continue-on-error: true

      - name: Upload code quality results
        uses: actions/upload-artifact@v4
        with:
          name: code-quality-results
          path: ${{ env.TEST_OUTPUT_DIR }}

  # Unit tests
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: [pre-flight, code-quality]
    if: needs.pre-flight.outputs.should-run-tests == 'true' && (needs.pre-flight.outputs.test-suite == 'all' || needs.pre-flight.outputs.test-suite == 'unit')
    strategy:
      matrix:
        node-version: ['18', '20']
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
          cache-dependency-path: mvs-vr-v2/implementation/server/package-lock.json

      - name: Install dependencies
        run: |
          cd mvs-vr-v2/implementation/server
          npm ci

      - name: Run unit tests
        run: |
          cd mvs-vr-v2/implementation/server
          npm run test:unit -- --coverage --reporter=json --outputFile=${{ env.TEST_OUTPUT_DIR }}/unit-test-results.json
        env:
          NODE_ENV: test

      - name: Generate coverage report
        run: |
          cd mvs-vr-v2/implementation/server
          npm run test:coverage -- --reporter=json-summary --outputFile=${{ env.COVERAGE_OUTPUT_DIR }}/coverage-summary.json

      - name: Upload test results
        uses: actions/upload-artifact@v4
        with:
          name: unit-test-results-node-${{ matrix.node-version }}
          path: |
            ${{ env.TEST_OUTPUT_DIR }}
            ${{ env.COVERAGE_OUTPUT_DIR }}

  # Integration tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [pre-flight, unit-tests]
    if: needs.pre-flight.outputs.should-run-tests == 'true' && (needs.pre-flight.outputs.test-suite == 'all' || needs.pre-flight.outputs.test-suite == 'integration')
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: mvs_vr_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: mvs-vr-v2/implementation/server/package-lock.json

      - name: Install dependencies
        run: |
          cd mvs-vr-v2/implementation/server
          npm ci

      - name: Setup test environment
        run: |
          cd mvs-vr-v2/implementation/server
          cp .env.example .env.test
          echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/mvs_vr_test" >> .env.test
          echo "REDIS_URL=redis://localhost:6379" >> .env.test

      - name: Run database migrations
        run: |
          cd mvs-vr-v2/implementation/server
          npm run db:migrate:test

      - name: Run integration tests
        run: |
          cd mvs-vr-v2/implementation/server
          npm run test:integration -- --reporter=json --outputFile=${{ env.TEST_OUTPUT_DIR }}/integration-test-results.json
        env:
          NODE_ENV: test

      - name: Upload integration test results
        uses: actions/upload-artifact@v4
        with:
          name: integration-test-results
          path: ${{ env.TEST_OUTPUT_DIR }}

  # End-to-End tests
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [pre-flight, integration-tests]
    if: needs.pre-flight.outputs.should-run-tests == 'true' && (needs.pre-flight.outputs.test-suite == 'all' || needs.pre-flight.outputs.test-suite == 'e2e')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: mvs-vr-v2/implementation/server/package-lock.json

      - name: Install dependencies
        run: |
          cd mvs-vr-v2/implementation/server
          npm ci

      - name: Install Playwright browsers
        run: |
          cd mvs-vr-v2/implementation/server
          npx playwright install --with-deps

      - name: Start services for E2E testing
        run: |
          cd mvs-vr-v2/implementation/server
          docker-compose -f docker-compose.test.yml up -d
          sleep 30  # Wait for services to be ready

      - name: Run E2E tests
        run: |
          cd mvs-vr-v2/implementation/server
          npm run test:e2e -- --reporter=json --outputFile=${{ env.TEST_OUTPUT_DIR }}/e2e-test-results.json
        env:
          NODE_ENV: test

      - name: Upload E2E test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-test-results
          path: |
            ${{ env.TEST_OUTPUT_DIR }}
            mvs-vr-v2/implementation/server/test-results/

      - name: Upload E2E screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: e2e-screenshots
          path: mvs-vr-v2/implementation/server/test-results/screenshots/

  # Load tests
  load-tests:
    name: Load Tests
    runs-on: ubuntu-latest
    needs: [pre-flight, integration-tests]
    if: needs.pre-flight.outputs.should-run-tests == 'true' && (needs.pre-flight.outputs.test-suite == 'all' || needs.pre-flight.outputs.test-suite == 'load')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: mvs-vr-v2/implementation/server/package-lock.json

      - name: Install dependencies
        run: |
          cd mvs-vr-v2/implementation/server
          npm ci

      - name: Install K6
        run: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: Start test environment
        run: |
          cd mvs-vr-v2/implementation/server
          docker-compose -f docker-compose.test.yml up -d
          sleep 30

      - name: Run load tests
        run: |
          cd mvs-vr-v2/implementation/server
          k6 run tests/load/advanced-load-testing-framework.js --out json=${{ env.PERFORMANCE_TEST_OUTPUT }}/load-test-results.json
        env:
          LOAD_TEST_BASE_URL: http://localhost:3000
          LOAD_TEST_DURATION: 300 # 5 minutes for CI

      - name: Upload load test results
        uses: actions/upload-artifact@v4
        with:
          name: load-test-results
          path: ${{ env.PERFORMANCE_TEST_OUTPUT }}

  # Performance monitoring
  performance-monitoring:
    name: Performance Monitoring
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-tests, load-tests]
    if: always() && needs.pre-flight.outputs.should-run-tests == 'true'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all test artifacts
        uses: actions/download-artifact@v4
        with:
          path: test-artifacts

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install monitoring dependencies
        run: |
          npm install -g @k6/summary-reporter
          cd mvs-vr-v2/implementation/server
          npm ci

      - name: Analyze test results
        run: |
          cd mvs-vr-v2/implementation/server
          node tests/monitoring/analyze-test-results.js test-artifacts/
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Generate performance report
        run: |
          cd mvs-vr-v2/implementation/server
          node tests/monitoring/generate-performance-report.js test-artifacts/ > performance-report.md

      - name: Comment PR with results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const report = fs.readFileSync('mvs-vr-v2/implementation/server/performance-report.md', 'utf8');

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 🧪 Test Results\n\n${report}`
            });

      - name: Upload performance report
        uses: actions/upload-artifact@v4
        with:
          name: performance-report
          path: mvs-vr-v2/implementation/server/performance-report.md

  # Build and deploy
  build-and-deploy:
    name: Build & Deploy
    runs-on: ubuntu-latest
    needs:
      [pre-flight, code-quality, unit-tests, integration-tests, e2e-tests, performance-monitoring]
    if: always() && (needs.unit-tests.result == 'success' || needs.pre-flight.outputs.should-run-tests == 'false')
    environment: ${{ needs.pre-flight.outputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: mvs-vr-v2/implementation/server/package-lock.json

      - name: Install dependencies
        run: |
          cd mvs-vr-v2/implementation/server
          npm ci

      - name: Build application
        run: |
          cd mvs-vr-v2/implementation/server
          npm run build
        env:
          NODE_ENV: production

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: mvs-vr-v2/implementation/server
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Deploy to staging
        if: needs.pre-flight.outputs.environment == 'staging'
        run: |
          echo "Deploying to staging environment..."
          # Add staging deployment commands here

      - name: Deploy to production
        if: needs.pre-flight.outputs.environment == 'production' && github.ref == 'refs/heads/main'
        run: |
          echo "Deploying to production environment..."
          # Add production deployment commands here

      - name: Run smoke tests
        run: |
          cd mvs-vr-v2/implementation/server
          npm run test:smoke
        env:
          TEST_URL: ${{ needs.pre-flight.outputs.environment == 'production' && 'https://mvs.kanousai.com' || 'https://staging.mvs.kanousai.com' }}

      - name: Notify deployment status
        if: always()
        uses: actions/github-script@v7
        with:
          script: |
            const status = '${{ job.status }}';
            const environment = '${{ needs.pre-flight.outputs.environment }}';
            const emoji = status === 'success' ? '✅' : '❌';

            github.rest.repos.createCommitStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              sha: context.sha,
              state: status === 'success' ? 'success' : 'failure',
              description: `Deployment to ${environment} ${status}`,
              context: `deployment/${environment}`
            });
