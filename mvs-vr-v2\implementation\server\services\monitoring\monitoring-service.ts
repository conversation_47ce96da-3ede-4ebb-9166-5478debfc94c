/**
 * Monitoring Service
 * Provides comprehensive monitoring and metrics collection
 */

import { EventEmitter } from 'events';
import { Logger } from '../../shared/utils/logger';

const logger = new Logger();

export interface MetricData {
  name: string;
  value: number;
  timestamp: Date;
  tags?: Record<string, string>;
  type: 'counter' | 'gauge' | 'histogram' | 'timer';
}

export interface AlertRule {
  id: string;
  name: string;
  metric: string;
  condition: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
  threshold: number;
  duration: number; // in milliseconds
  enabled: boolean;
  actions: AlertAction[];
}

export interface AlertAction {
  type: 'email' | 'webhook' | 'log';
  config: Record<string, any>;
}

export interface Alert {
  id: string;
  ruleId: string;
  metric: string;
  value: number;
  threshold: number;
  condition: string;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
}

export interface HealthCheck {
  name: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  lastCheck: Date;
  responseTime: number;
  error?: string;
  details?: Record<string, any>;
}

export class MonitoringService extends EventEmitter {
  private metrics: Map<string, MetricData[]> = new Map();
  private alertRules: Map<string, AlertRule> = new Map();
  private activeAlerts: Map<string, Alert> = new Map();
  private healthChecks: Map<string, HealthCheck> = new Map();
  private isRunning: boolean = false;
  private checkInterval: NodeJS.Timeout | null = null;

  constructor() {
    super();
    this.setupDefaultHealthChecks();
  }

  /**
   * Start the monitoring service
   */
  start(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    
    // Start periodic health checks
    this.checkInterval = setInterval(() => {
      this.runHealthChecks();
      this.checkAlerts();
    }, 30000); // Every 30 seconds

    logger.info('Monitoring service started');
    this.emit('started');
  }

  /**
   * Stop the monitoring service
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }

    logger.info('Monitoring service stopped');
    this.emit('stopped');
  }

  /**
   * Record a metric
   */
  recordMetric(metric: Omit<MetricData, 'timestamp'>): void {
    const metricData: MetricData = {
      ...metric,
      timestamp: new Date(),
    };

    const existing = this.metrics.get(metric.name) || [];
    existing.push(metricData);

    // Keep only last 1000 data points per metric
    if (existing.length > 1000) {
      existing.splice(0, existing.length - 1000);
    }

    this.metrics.set(metric.name, existing);

    this.emit('metric', metricData);
  }

  /**
   * Get metrics by name
   */
  getMetrics(name: string, since?: Date): MetricData[] {
    const metrics = this.metrics.get(name) || [];
    
    if (since) {
      return metrics.filter(m => m.timestamp >= since);
    }
    
    return metrics;
  }

  /**
   * Get all metric names
   */
  getMetricNames(): string[] {
    return Array.from(this.metrics.keys());
  }

  /**
   * Add an alert rule
   */
  addAlertRule(rule: AlertRule): void {
    this.alertRules.set(rule.id, rule);
    logger.info('Alert rule added', { ruleId: rule.id, name: rule.name });
  }

  /**
   * Remove an alert rule
   */
  removeAlertRule(ruleId: string): void {
    this.alertRules.delete(ruleId);
    
    // Remove any active alerts for this rule
    for (const [alertId, alert] of this.activeAlerts.entries()) {
      if (alert.ruleId === ruleId) {
        this.activeAlerts.delete(alertId);
      }
    }
    
    logger.info('Alert rule removed', { ruleId });
  }

  /**
   * Get all alert rules
   */
  getAlertRules(): AlertRule[] {
    return Array.from(this.alertRules.values());
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): Alert[] {
    return Array.from(this.activeAlerts.values()).filter(a => !a.resolved);
  }

  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string): void {
    const alert = this.activeAlerts.get(alertId);
    if (alert && !alert.resolved) {
      alert.resolved = true;
      alert.resolvedAt = new Date();
      
      logger.info('Alert resolved', { alertId, ruleId: alert.ruleId });
      this.emit('alertResolved', alert);
    }
  }

  /**
   * Add a health check
   */
  addHealthCheck(name: string, checkFn: () => Promise<Omit<HealthCheck, 'name' | 'lastCheck'>>): void {
    // Store the check function for later execution
    (this as any)[`healthCheck_${name}`] = checkFn;
    
    logger.info('Health check added', { name });
  }

  /**
   * Remove a health check
   */
  removeHealthCheck(name: string): void {
    this.healthChecks.delete(name);
    delete (this as any)[`healthCheck_${name}`];
    
    logger.info('Health check removed', { name });
  }

  /**
   * Get health check status
   */
  getHealthCheck(name: string): HealthCheck | undefined {
    return this.healthChecks.get(name);
  }

  /**
   * Get all health checks
   */
  getAllHealthChecks(): HealthCheck[] {
    return Array.from(this.healthChecks.values());
  }

  /**
   * Get overall system health
   */
  getSystemHealth(): { status: 'healthy' | 'unhealthy' | 'degraded'; checks: HealthCheck[] } {
    const checks = this.getAllHealthChecks();
    
    if (checks.length === 0) {
      return { status: 'healthy', checks: [] };
    }
    
    const unhealthyCount = checks.filter(c => c.status === 'unhealthy').length;
    const degradedCount = checks.filter(c => c.status === 'degraded').length;
    
    let status: 'healthy' | 'unhealthy' | 'degraded';
    
    if (unhealthyCount > 0) {
      status = 'unhealthy';
    } else if (degradedCount > 0) {
      status = 'degraded';
    } else {
      status = 'healthy';
    }
    
    return { status, checks };
  }

  /**
   * Run all health checks
   */
  private async runHealthChecks(): Promise<void> {
    const checkPromises: Promise<void>[] = [];
    
    // Run custom health checks
    for (const [name, _] of this.healthChecks.entries()) {
      const checkFn = (this as any)[`healthCheck_${name}`];
      if (typeof checkFn === 'function') {
        checkPromises.push(this.runSingleHealthCheck(name, checkFn));
      }
    }
    
    await Promise.allSettled(checkPromises);
  }

  /**
   * Run a single health check
   */
  private async runSingleHealthCheck(name: string, checkFn: () => Promise<Omit<HealthCheck, 'name' | 'lastCheck'>>): Promise<void> {
    const startTime = Date.now();
    
    try {
      const result = await checkFn();
      const responseTime = Date.now() - startTime;
      
      const healthCheck: HealthCheck = {
        name,
        lastCheck: new Date(),
        responseTime,
        ...result,
      };
      
      this.healthChecks.set(name, healthCheck);
      this.emit('healthCheck', healthCheck);
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      const healthCheck: HealthCheck = {
        name,
        status: 'unhealthy',
        lastCheck: new Date(),
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
      
      this.healthChecks.set(name, healthCheck);
      this.emit('healthCheck', healthCheck);
      
      logger.error('Health check failed', { name, error });
    }
  }

  /**
   * Check alert rules against current metrics
   */
  private checkAlerts(): void {
    for (const rule of this.alertRules.values()) {
      if (!rule.enabled) {
        continue;
      }
      
      this.checkAlertRule(rule);
    }
  }

  /**
   * Check a single alert rule
   */
  private checkAlertRule(rule: AlertRule): void {
    const metrics = this.getMetrics(rule.metric);
    
    if (metrics.length === 0) {
      return;
    }
    
    const latestMetric = metrics[metrics.length - 1];
    const shouldAlert = this.evaluateCondition(latestMetric.value, rule.condition, rule.threshold);
    
    const existingAlert = Array.from(this.activeAlerts.values())
      .find(a => a.ruleId === rule.id && !a.resolved);
    
    if (shouldAlert && !existingAlert) {
      // Create new alert
      const alert: Alert = {
        id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ruleId: rule.id,
        metric: rule.metric,
        value: latestMetric.value,
        threshold: rule.threshold,
        condition: rule.condition,
        timestamp: new Date(),
        resolved: false,
      };
      
      this.activeAlerts.set(alert.id, alert);
      
      logger.warn('Alert triggered', {
        alertId: alert.id,
        ruleId: rule.id,
        metric: rule.metric,
        value: latestMetric.value,
        threshold: rule.threshold,
      });
      
      this.emit('alert', alert);
      this.executeAlertActions(rule, alert);
      
    } else if (!shouldAlert && existingAlert) {
      // Resolve existing alert
      this.resolveAlert(existingAlert.id);
    }
  }

  /**
   * Evaluate alert condition
   */
  private evaluateCondition(value: number, condition: string, threshold: number): boolean {
    switch (condition) {
      case 'gt': return value > threshold;
      case 'gte': return value >= threshold;
      case 'lt': return value < threshold;
      case 'lte': return value <= threshold;
      case 'eq': return value === threshold;
      default: return false;
    }
  }

  /**
   * Execute alert actions
   */
  private executeAlertActions(rule: AlertRule, alert: Alert): void {
    for (const action of rule.actions) {
      try {
        this.executeAlertAction(action, rule, alert);
      } catch (error) {
        logger.error('Failed to execute alert action', {
          actionType: action.type,
          ruleId: rule.id,
          alertId: alert.id,
          error,
        });
      }
    }
  }

  /**
   * Execute a single alert action
   */
  private executeAlertAction(action: AlertAction, rule: AlertRule, alert: Alert): void {
    switch (action.type) {
      case 'log':
        logger.warn('Alert action: log', {
          ruleName: rule.name,
          metric: alert.metric,
          value: alert.value,
          threshold: alert.threshold,
        });
        break;
        
      case 'email':
        // Email implementation would go here
        logger.info('Alert action: email (not implemented)', {
          to: action.config.to,
          subject: action.config.subject,
        });
        break;
        
      case 'webhook':
        // Webhook implementation would go here
        logger.info('Alert action: webhook (not implemented)', {
          url: action.config.url,
        });
        break;
    }
  }

  /**
   * Setup default health checks
   */
  private setupDefaultHealthChecks(): void {
    // Memory usage check
    this.addHealthCheck('memory', async () => {
      const memUsage = process.memoryUsage();
      const heapUsedMB = memUsage.heapUsed / 1024 / 1024;
      const heapTotalMB = memUsage.heapTotal / 1024 / 1024;
      const usage = (heapUsedMB / heapTotalMB) * 100;
      
      let status: 'healthy' | 'degraded' | 'unhealthy';
      if (usage > 90) {
        status = 'unhealthy';
      } else if (usage > 75) {
        status = 'degraded';
      } else {
        status = 'healthy';
      }
      
      return {
        status,
        details: {
          heapUsedMB: Math.round(heapUsedMB),
          heapTotalMB: Math.round(heapTotalMB),
          usagePercent: Math.round(usage),
        },
      };
    });
  }
}

export default MonitoringService;
