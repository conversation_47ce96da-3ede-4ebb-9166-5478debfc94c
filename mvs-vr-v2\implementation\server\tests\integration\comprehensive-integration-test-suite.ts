/**
 * Comprehensive Integration Test Suite
 *
 * Tests the interaction between all major system components:
 * - Frontend ↔ Backend API
 * - Backend ↔ Directus CMS
 * - Backend ↔ Supabase
 * - Real-time WebSocket communication
 * - Authentication flows
 * - Asset management workflows
 * - Visual Editors integration
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { createClient } from '@supabase/supabase-js';
import axios from 'axios';
import WebSocket from 'ws';
import { performance } from 'perf_hooks';

// Test configuration
const TEST_CONFIG = {
  api: {
    baseUrl: process.env.TEST_API_BASE_URL || 'http://localhost:3000',
    timeout: 30000,
  },
  directus: {
    url: process.env.TEST_DIRECTUS_URL || 'http://localhost:8055',
    email: process.env.TEST_DIRECTUS_EMAIL || '<EMAIL>',
    password: process.env.TEST_DIRECTUS_PASSWORD || 'admin',
  },
  supabase: {
    url: process.env.TEST_SUPABASE_URL || 'https://hiyqiqbgiueyyvqoqhht.supabase.co',
    anonKey: process.env.TEST_SUPABASE_ANON_KEY || '',
    serviceKey: process.env.TEST_SUPABASE_SERVICE_KEY || '',
  },
  websocket: {
    url: process.env.TEST_WS_URL || 'ws://localhost:3000',
  },
  performance: {
    maxResponseTime: 2000, // 2 seconds
    maxDbQueryTime: 1000, // 1 second
  },
};

// Test data
const testData = {
  vendor: {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    company: 'Test Vendor Company',
  },
  client: {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    name: 'Test Client',
  },
  showroom: {
    name: 'Integration Test Showroom',
    description: 'Test showroom for integration testing',
  },
  asset: {
    name: 'Test Asset',
    type: 'model',
    url: 'https://example.com/test-asset.glb',
  },
};

// Global test state
let supabaseClient: any;
let directusToken: string;
let vendorToken: string;
let clientToken: string;
let testShowroomId: string;
let testAssetId: string;

describe('Comprehensive Integration Tests', () => {
  beforeAll(async () => {
    // Initialize Supabase client
    supabaseClient = createClient(TEST_CONFIG.supabase.url, TEST_CONFIG.supabase.anonKey);

    // Authenticate with Directus
    directusToken = await authenticateDirectus();

    // Setup test data
    await setupTestData();
  }, 60000);

  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
  }, 30000);

  describe('Authentication Integration', () => {
    it('should handle vendor authentication flow across all systems', async () => {
      const startTime = performance.now();

      // 1. Register vendor in Supabase
      const { data: authData, error: authError } = await supabaseClient.auth.signUp({
        email: testData.vendor.email,
        password: testData.vendor.password,
      });

      expect(authError).toBeNull();
      expect(authData.user).toBeDefined();

      // 2. Create vendor profile in Directus
      const directusResponse = await axios.post(
        `${TEST_CONFIG.directus.url}/items/vendors`,
        {
          email: testData.vendor.email,
          company: testData.vendor.company,
          supabase_user_id: authData.user?.id,
        },
        {
          headers: { Authorization: `Bearer ${directusToken}` },
        },
      );

      expect(directusResponse.status).toBe(200);

      // 3. Login through API
      const loginResponse = await axios.post(`${TEST_CONFIG.api.baseUrl}/api/auth/login`, {
        email: testData.vendor.email,
        password: testData.vendor.password,
      });

      expect(loginResponse.status).toBe(200);
      expect(loginResponse.data.token).toBeDefined();
      vendorToken = loginResponse.data.token;

      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(TEST_CONFIG.performance.maxResponseTime);
    });

    it('should sync user data between Supabase and Directus', async () => {
      // Update user profile in Supabase
      const { error: updateError } = await supabaseClient.auth.updateUser({
        data: { company: 'Updated Company Name' },
      });

      expect(updateError).toBeNull();

      // Verify update is reflected in Directus
      const directusResponse = await axios.get(
        `${TEST_CONFIG.directus.url}/items/vendors?filter[email][_eq]=${testData.vendor.email}`,
        {
          headers: { Authorization: `Bearer ${directusToken}` },
        },
      );

      expect(directusResponse.data.data[0].company).toBe('Updated Company Name');
    });
  });

  describe('Asset Management Integration', () => {
    it('should handle complete asset upload and processing workflow', async () => {
      const startTime = performance.now();

      // 1. Upload asset metadata to Directus
      const assetResponse = await axios.post(
        `${TEST_CONFIG.directus.url}/items/assets`,
        testData.asset,
        {
          headers: { Authorization: `Bearer ${directusToken}` },
        },
      );

      expect(assetResponse.status).toBe(200);
      testAssetId = assetResponse.data.data.id;

      // 2. Process asset through API
      const processResponse = await axios.post(
        `${TEST_CONFIG.api.baseUrl}/api/assets/process`,
        { assetId: testAssetId },
        {
          headers: { Authorization: `Bearer ${vendorToken}` },
        },
      );

      expect(processResponse.status).toBe(200);

      // 3. Verify asset is available in Supabase storage
      const { data: storageData, error: storageError } = await supabaseClient.storage
        .from('assets')
        .list('processed');

      expect(storageError).toBeNull();
      expect(storageData).toBeDefined();

      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(TEST_CONFIG.performance.maxResponseTime * 2);
    });

    it('should handle asset versioning and rollback', async () => {
      // Create asset version
      const versionResponse = await axios.post(
        `${TEST_CONFIG.api.baseUrl}/api/assets/${testAssetId}/versions`,
        { description: 'Test version' },
        {
          headers: { Authorization: `Bearer ${vendorToken}` },
        },
      );

      expect(versionResponse.status).toBe(200);

      // Rollback to previous version
      const rollbackResponse = await axios.post(
        `${TEST_CONFIG.api.baseUrl}/api/assets/${testAssetId}/rollback`,
        { versionId: versionResponse.data.versionId },
        {
          headers: { Authorization: `Bearer ${vendorToken}` },
        },
      );

      expect(rollbackResponse.status).toBe(200);
    });
  });

  describe('Real-time Communication Integration', () => {
    it('should handle WebSocket connections and real-time updates', async () => {
      return new Promise((resolve, reject) => {
        const ws = new WebSocket(`${TEST_CONFIG.websocket.url}/ws`);
        let messageReceived = false;

        ws.on('open', () => {
          // Subscribe to showroom updates
          ws.send(
            JSON.stringify({
              type: 'subscribe',
              channel: `showroom:${testShowroomId}`,
              token: vendorToken,
            }),
          );

          // Trigger an update
          setTimeout(async () => {
            await axios.patch(
              `${TEST_CONFIG.api.baseUrl}/api/showrooms/${testShowroomId}`,
              { description: 'Updated via WebSocket test' },
              {
                headers: { Authorization: `Bearer ${vendorToken}` },
              },
            );
          }, 1000);
        });

        ws.on('message', data => {
          const message = JSON.parse(data.toString());
          if (message.type === 'showroom_updated') {
            messageReceived = true;
            ws.close();
            resolve(true);
          }
        });

        ws.on('error', error => {
          reject(error);
        });

        // Timeout after 10 seconds
        setTimeout(() => {
          if (!messageReceived) {
            ws.close();
            reject(new Error('WebSocket message not received within timeout'));
          }
        }, 10000);
      });
    });
  });

  describe('Visual Editors Integration', () => {
    it('should handle showroom layout editor workflow', async () => {
      // Create layout configuration
      const layoutConfig = {
        showroomId: testShowroomId,
        layout: {
          type: 'grid',
          columns: 3,
          rows: 2,
          items: [{ id: testAssetId, position: { x: 0, y: 0 } }],
        },
      };

      const layoutResponse = await axios.post(
        `${TEST_CONFIG.api.baseUrl}/api/visual-editors/showroom-layout`,
        layoutConfig,
        {
          headers: { Authorization: `Bearer ${vendorToken}` },
        },
      );

      expect(layoutResponse.status).toBe(200);
      expect(layoutResponse.data.layoutId).toBeDefined();

      // Verify layout is saved in Directus
      const directusResponse = await axios.get(
        `${TEST_CONFIG.directus.url}/items/showroom_layouts/${layoutResponse.data.layoutId}`,
        {
          headers: { Authorization: `Bearer ${directusToken}` },
        },
      );

      expect(directusResponse.status).toBe(200);
      expect(directusResponse.data.data.layout).toEqual(layoutConfig.layout);
    });

    it('should handle material texture editor workflow', async () => {
      const materialConfig = {
        assetId: testAssetId,
        materials: [
          {
            name: 'test_material',
            baseColor: '#ff0000',
            metallic: 0.5,
            roughness: 0.3,
            textures: {
              baseColor: 'texture_url_1',
              normal: 'texture_url_2',
            },
          },
        ],
      };

      const materialResponse = await axios.post(
        `${TEST_CONFIG.api.baseUrl}/api/visual-editors/material-texture`,
        materialConfig,
        {
          headers: { Authorization: `Bearer ${vendorToken}` },
        },
      );

      expect(materialResponse.status).toBe(200);
      expect(materialResponse.data.materialId).toBeDefined();
    });

    it('should handle lighting editor workflow', async () => {
      const lightingConfig = {
        showroomId: testShowroomId,
        lighting: {
          ambient: {
            intensity: 0.3,
            color: '#ffffff',
          },
          directional: [
            {
              intensity: 1.0,
              color: '#ffffff',
              direction: { x: 1, y: -1, z: 0 },
              castShadows: true,
            },
          ],
          point: [
            {
              intensity: 0.8,
              color: '#ffaa00',
              position: { x: 0, y: 5, z: 0 },
              range: 10,
            },
          ],
        },
      };

      const lightingResponse = await axios.post(
        `${TEST_CONFIG.api.baseUrl}/api/visual-editors/lighting`,
        lightingConfig,
        {
          headers: { Authorization: `Bearer ${vendorToken}` },
        },
      );

      expect(lightingResponse.status).toBe(200);
      expect(lightingResponse.data.lightingId).toBeDefined();
    });

    it('should handle product configurator workflow', async () => {
      const configData = {
        assetId: testAssetId,
        configuration: {
          variants: [
            {
              name: 'Color',
              options: ['Red', 'Blue', 'Green'],
              default: 'Red',
            },
            {
              name: 'Size',
              options: ['Small', 'Medium', 'Large'],
              default: 'Medium',
            },
          ],
          rules: [
            {
              condition: { Color: 'Red', Size: 'Large' },
              action: { price: 150 },
            },
          ],
        },
      };

      const configResponse = await axios.post(
        `${TEST_CONFIG.api.baseUrl}/api/visual-editors/product-configurator`,
        configData,
        {
          headers: { Authorization: `Bearer ${vendorToken}` },
        },
      );

      expect(configResponse.status).toBe(200);
      expect(configResponse.data.configId).toBeDefined();
    });
  });

  describe('Performance Integration', () => {
    it('should maintain performance standards across all integrations', async () => {
      const performanceTests = [
        {
          name: 'Authentication',
          endpoint: '/api/auth/login',
          method: 'POST',
          data: { email: testData.vendor.email, password: testData.vendor.password },
        },
        {
          name: 'Asset List',
          endpoint: '/api/assets',
          method: 'GET',
          headers: { Authorization: `Bearer ${vendorToken}` },
        },
        {
          name: 'Showroom Details',
          endpoint: `/api/showrooms/${testShowroomId}`,
          method: 'GET',
          headers: { Authorization: `Bearer ${vendorToken}` },
        },
      ];

      for (const test of performanceTests) {
        const startTime = performance.now();

        const response = await axios({
          method: test.method,
          url: `${TEST_CONFIG.api.baseUrl}${test.endpoint}`,
          data: test.data,
          headers: test.headers,
        });

        const endTime = performance.now();
        const responseTime = endTime - startTime;

        expect(response.status).toBeLessThan(400);
        expect(responseTime).toBeLessThan(TEST_CONFIG.performance.maxResponseTime);

        console.log(`${test.name} response time: ${responseTime.toFixed(2)}ms`);
      }
    });
  });
});

// Helper functions
async function authenticateDirectus(): Promise<string> {
  const response = await axios.post(`${TEST_CONFIG.directus.url}/auth/login`, {
    email: TEST_CONFIG.directus.email,
    password: TEST_CONFIG.directus.password,
  });

  return response.data.data.access_token;
}

async function setupTestData(): Promise<void> {
  // Create test showroom
  const showroomResponse = await axios.post(
    `${TEST_CONFIG.directus.url}/items/showrooms`,
    testData.showroom,
    {
      headers: { Authorization: `Bearer ${directusToken}` },
    },
  );

  testShowroomId = showroomResponse.data.data.id;
}

async function cleanupTestData(): Promise<void> {
  try {
    // Delete test showroom
    if (testShowroomId) {
      await axios.delete(`${TEST_CONFIG.directus.url}/items/showrooms/${testShowroomId}`, {
        headers: { Authorization: `Bearer ${directusToken}` },
      });
    }

    // Delete test asset
    if (testAssetId) {
      await axios.delete(`${TEST_CONFIG.directus.url}/items/assets/${testAssetId}`, {
        headers: { Authorization: `Bearer ${directusToken}` },
      });
    }

    // Delete test users from Supabase
    if (supabaseClient) {
      await supabaseClient.auth.signOut();
    }
  } catch (error) {
    console.warn('Cleanup error:', error);
  }
}
