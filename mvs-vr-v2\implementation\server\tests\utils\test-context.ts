/**
 * Test Context Utilities
 * Provides utilities for managing test context and cleanup
 */

import { vi } from 'vitest';

interface TestContext {
  mocks: Map<string, any>;
  cleanup: () => void;
  reset: () => void;
  addMock: (name: string, mock: any) => void;
  getMock: (name: string) => any;
}

interface TestEnvironment {
  name: 'local' | 'staging' | 'production';
  config: Record<string, any>;
}

// Global test context
let currentContext: TestContext | null = null;
let currentEnvironment: TestEnvironment = {
  name: 'local',
  config: {},
};

/**
 * Create a new test context
 */
export function createTestContext(): TestContext {
  const mocks = new Map<string, any>();

  const context: TestContext = {
    mocks,

    cleanup: () => {
      // Clear all mocks
      vi.clearAllMocks();

      // Reset all stored mocks
      mocks.clear();

      // Reset environment to local
      currentEnvironment = {
        name: 'local',
        config: {},
      };
    },

    reset: () => {
      // Reset all mocks without clearing them
      vi.resetAllMocks();

      // Clear stored mocks
      mocks.clear();
    },

    addMock: (name: string, mock: any) => {
      mocks.set(name, mock);
    },

    getMock: (name: string) => {
      return mocks.get(name);
    },
  };

  currentContext = context;
  return context;
}

/**
 * Get the current test context
 */
export function getTestContext(): TestContext {
  if (!currentContext) {
    currentContext = createTestContext();
  }
  return currentContext;
}

/**
 * Switch test environment
 */
export function switchTestEnvironment(environment: 'local' | 'staging' | 'production') {
  currentEnvironment.name = environment;

  // Set environment-specific configuration
  switch (environment) {
    case 'local':
      currentEnvironment.config = {
        supabaseUrl: 'http://localhost:54321',
        supabaseKey: 'test-key',
        redisUrl: 'redis://localhost:6379',
        apiUrl: 'http://localhost:3000',
      };
      break;

    case 'staging':
      currentEnvironment.config = {
        supabaseUrl: 'https://hiyqiqbgiueyyvqoqhht.supabase.co',
        supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'test-key',
        redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',
        apiUrl: 'https://staging-api.mvs.kanousai.com',
      };
      break;

    case 'production':
      currentEnvironment.config = {
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
        supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        redisUrl: process.env.REDIS_URL || '',
        apiUrl: 'https://api.mvs.kanousai.com',
      };
      break;
  }
}

/**
 * Get current test environment
 */
export function getTestEnvironment(): TestEnvironment {
  return currentEnvironment;
}

/**
 * Create mock services for testing
 */
export function createMockServices() {
  const context = getTestContext();

  // Mock Supabase client
  const mockSupabase = {
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    neq: vi.fn().mockReturnThis(),
    gt: vi.fn().mockReturnThis(),
    gte: vi.fn().mockReturnThis(),
    lt: vi.fn().mockReturnThis(),
    lte: vi.fn().mockReturnThis(),
    like: vi.fn().mockReturnThis(),
    ilike: vi.fn().mockReturnThis(),
    is: vi.fn().mockReturnThis(),
    in: vi.fn().mockReturnThis(),
    contains: vi.fn().mockReturnThis(),
    containedBy: vi.fn().mockReturnThis(),
    rangeGt: vi.fn().mockReturnThis(),
    rangeGte: vi.fn().mockReturnThis(),
    rangeLt: vi.fn().mockReturnThis(),
    rangeLte: vi.fn().mockReturnThis(),
    rangeAdjacent: vi.fn().mockReturnThis(),
    overlaps: vi.fn().mockReturnThis(),
    textSearch: vi.fn().mockReturnThis(),
    match: vi.fn().mockReturnThis(),
    not: vi.fn().mockReturnThis(),
    or: vi.fn().mockReturnThis(),
    filter: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    range: vi.fn().mockReturnThis(),
    single: vi.fn().mockReturnThis(),
    maybeSingle: vi.fn().mockReturnThis(),
    csv: vi.fn().mockReturnThis(),
    geojson: vi.fn().mockReturnThis(),
    explain: vi.fn().mockReturnThis(),
    rollback: vi.fn().mockReturnThis(),
    returns: vi.fn().mockReturnThis(),
    storage: {
      from: vi.fn().mockReturnValue({
        upload: vi.fn().mockResolvedValue({ data: { path: 'test-path' }, error: null }),
        download: vi.fn().mockResolvedValue({ data: Buffer.from('test'), error: null }),
        getPublicUrl: vi.fn().mockReturnValue({ data: { publicUrl: 'https://example.com/test' } }),
        remove: vi.fn().mockResolvedValue({ data: null, error: null }),
        list: vi.fn().mockResolvedValue({ data: [], error: null }),
        createSignedUrl: vi
          .fn()
          .mockResolvedValue({ data: { signedUrl: 'https://example.com/signed' }, error: null }),
      }),
    },
    auth: {
      signUp: vi.fn().mockResolvedValue({ data: { user: { id: 'test-user' } }, error: null }),
      signInWithPassword: vi
        .fn()
        .mockResolvedValue({ data: { user: { id: 'test-user' } }, error: null }),
      signOut: vi.fn().mockResolvedValue({ error: null }),
      getUser: vi.fn().mockResolvedValue({ data: { user: { id: 'test-user' } }, error: null }),
      getSession: vi
        .fn()
        .mockResolvedValue({ data: { session: { access_token: 'test-token' } }, error: null }),
    },
  };

  // Mock Redis client
  const mockRedis = {
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue('OK'),
    del: vi.fn().mockResolvedValue(1),
    exists: vi.fn().mockResolvedValue(0),
    expire: vi.fn().mockResolvedValue(1),
    ttl: vi.fn().mockResolvedValue(-1),
    keys: vi.fn().mockResolvedValue([]),
    flushall: vi.fn().mockResolvedValue('OK'),
    quit: vi.fn().mockResolvedValue('OK'),
    disconnect: vi.fn().mockResolvedValue(undefined),
  };

  // Mock file system
  const mockFs = {
    promises: {
      readFile: vi.fn().mockResolvedValue(Buffer.from('test')),
      writeFile: vi.fn().mockResolvedValue(undefined),
      unlink: vi.fn().mockResolvedValue(undefined),
      mkdir: vi.fn().mockResolvedValue(undefined),
      rmdir: vi.fn().mockResolvedValue(undefined),
      stat: vi.fn().mockResolvedValue({ isFile: () => true, isDirectory: () => false }),
      access: vi.fn().mockResolvedValue(undefined),
    },
    existsSync: vi.fn().mockReturnValue(true),
    mkdirSync: vi.fn().mockReturnValue(undefined),
    readFileSync: vi.fn().mockReturnValue(Buffer.from('test')),
    writeFileSync: vi.fn().mockReturnValue(undefined),
  };

  // Store mocks in context
  context.addMock('supabase', mockSupabase);
  context.addMock('redis', mockRedis);
  context.addMock('fs', mockFs);

  return {
    mockSupabase,
    mockRedis,
    mockFs,
  };
}

/**
 * Setup test environment with common mocks
 */
export function setupTestEnvironment() {
  const context = createTestContext();
  const services = createMockServices();

  // Switch to local environment by default
  switchTestEnvironment('local');

  return {
    context,
    services,
    cleanup: context.cleanup,
    reset: context.reset,
  };
}

/**
 * Create mock request object
 */
export function createMockRequest(options: any = {}) {
  return {
    method: options.method || 'GET',
    url: options.url || '/',
    headers: options.headers || {},
    body: options.body || {},
    params: options.params || {},
    query: options.query || {},
    user: options.user || null,
    ...options,
  };
}

/**
 * Create mock response object
 */
export function createMockResponse() {
  const res = {
    status: vi.fn().mockReturnThis(),
    json: vi.fn().mockReturnThis(),
    send: vi.fn().mockReturnThis(),
    set: vi.fn().mockReturnThis(),
    cookie: vi.fn().mockReturnThis(),
    clearCookie: vi.fn().mockReturnThis(),
    redirect: vi.fn().mockReturnThis(),
    end: vi.fn().mockReturnThis(),
  };
  return res;
}

/**
 * Create mock next function
 */
export function createMockNext() {
  return vi.fn();
}

/**
 * Create mock crypto functions
 */
export function createMockCrypto() {
  return {
    pbkdf2Sync: vi.fn(),
    randomBytes: vi.fn(),
    createHash: vi.fn(),
    createCipher: vi.fn(),
    createDecipher: vi.fn(),
  };
}

/**
 * Create mock JWT functions
 */
export function createMockJWT() {
  return {
    sign: vi.fn(),
    verify: vi.fn(),
    decode: vi.fn(),
  };
}

// Export the global test context for backward compatibility
export const testContext = {
  cleanup: () => getTestContext().cleanup(),
  reset: () => getTestContext().reset(),
  addMock: (name: string, mock: any) => getTestContext().addMock(name, mock),
  getMock: (name: string) => getTestContext().getMock(name),

  // Mock creation functions
  createRequest: createMockRequest,
  createResponse: createMockResponse,
  createNext: createMockNext,

  // Mock services
  crypto: createMockCrypto(),
  jwt: createMockJWT(),
  redis: {
    get: vi.fn(),
    set: vi.fn(),
    del: vi.fn(),
    expire: vi.fn(),
    exists: vi.fn(),
    keys: vi.fn(),
  },
};

export default {
  createTestContext,
  getTestContext,
  switchTestEnvironment,
  getTestEnvironment,
  createMockServices,
  setupTestEnvironment,
  testContext,
};
