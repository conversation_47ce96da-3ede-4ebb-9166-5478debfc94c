/**
 * Security Service
 * Provides comprehensive security functionality for the application
 */

import crypto from 'crypto';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { Logger } from '../../shared/utils/logger';

const logger = new Logger();

export interface SecurityConfig {
  jwtSecret: string;
  jwtExpiresIn: string;
  bcryptRounds: number;
  encryptionKey: string;
  sessionTimeout: number;
  maxLoginAttempts: number;
  lockoutDuration: number;
}

export interface UserSession {
  userId: string;
  sessionId: string;
  createdAt: Date;
  lastActivity: Date;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
}

export interface SecurityEvent {
  type: 'login' | 'logout' | 'failed_login' | 'password_change' | 'suspicious_activity';
  userId?: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  details: Record<string, any>;
}

export class SecurityService {
  private config: SecurityConfig;
  private activeSessions: Map<string, UserSession> = new Map();
  private loginAttempts: Map<string, { count: number; lastAttempt: Date }> = new Map();

  constructor(config: SecurityConfig) {
    this.config = config;
    this.startSessionCleanup();
  }

  /**
   * Hash a password using bcrypt
   */
  async hashPassword(password: string): Promise<string> {
    try {
      return await bcrypt.hash(password, this.config.bcryptRounds);
    } catch (error) {
      logger.error('Error hashing password', { error });
      throw new Error('Password hashing failed');
    }
  }

  /**
   * Verify a password against a hash
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      logger.error('Error verifying password', { error });
      return false;
    }
  }

  /**
   * Generate a JWT token
   */
  generateToken(payload: Record<string, any>): string {
    try {
      return jwt.sign(payload, this.config.jwtSecret, {
        expiresIn: this.config.jwtExpiresIn,
      });
    } catch (error) {
      logger.error('Error generating JWT token', { error });
      throw new Error('Token generation failed');
    }
  }

  /**
   * Verify a JWT token
   */
  verifyToken(token: string): Record<string, any> | null {
    try {
      return jwt.verify(token, this.config.jwtSecret) as Record<string, any>;
    } catch (error) {
      logger.warn('Invalid JWT token', { error: error.message });
      return null;
    }
  }

  /**
   * Encrypt sensitive data
   */
  encrypt(data: string): string {
    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher('aes-256-gcm', this.config.encryptionKey);
      cipher.setAAD(iv);
      
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
    } catch (error) {
      logger.error('Error encrypting data', { error });
      throw new Error('Encryption failed');
    }
  }

  /**
   * Decrypt sensitive data
   */
  decrypt(encryptedData: string): string {
    try {
      const parts = encryptedData.split(':');
      if (parts.length !== 3) {
        throw new Error('Invalid encrypted data format');
      }
      
      const iv = Buffer.from(parts[0], 'hex');
      const authTag = Buffer.from(parts[1], 'hex');
      const encrypted = parts[2];
      
      const decipher = crypto.createDecipher('aes-256-gcm', this.config.encryptionKey);
      decipher.setAAD(iv);
      decipher.setAuthTag(authTag);
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      logger.error('Error decrypting data', { error });
      throw new Error('Decryption failed');
    }
  }

  /**
   * Create a new user session
   */
  createSession(userId: string, ipAddress: string, userAgent: string): UserSession {
    const sessionId = crypto.randomUUID();
    const session: UserSession = {
      userId,
      sessionId,
      createdAt: new Date(),
      lastActivity: new Date(),
      ipAddress,
      userAgent,
      isActive: true,
    };
    
    this.activeSessions.set(sessionId, session);
    
    logger.info('User session created', { userId, sessionId, ipAddress });
    
    return session;
  }

  /**
   * Validate and update a session
   */
  validateSession(sessionId: string): UserSession | null {
    const session = this.activeSessions.get(sessionId);
    
    if (!session || !session.isActive) {
      return null;
    }
    
    // Check if session has expired
    const now = new Date();
    const sessionAge = now.getTime() - session.lastActivity.getTime();
    
    if (sessionAge > this.config.sessionTimeout) {
      this.destroySession(sessionId);
      return null;
    }
    
    // Update last activity
    session.lastActivity = now;
    this.activeSessions.set(sessionId, session);
    
    return session;
  }

  /**
   * Destroy a session
   */
  destroySession(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.isActive = false;
      this.activeSessions.delete(sessionId);
      
      logger.info('User session destroyed', { 
        userId: session.userId, 
        sessionId 
      });
    }
  }

  /**
   * Check if user is locked out due to failed login attempts
   */
  isUserLockedOut(identifier: string): boolean {
    const attempts = this.loginAttempts.get(identifier);
    
    if (!attempts) {
      return false;
    }
    
    if (attempts.count >= this.config.maxLoginAttempts) {
      const timeSinceLastAttempt = Date.now() - attempts.lastAttempt.getTime();
      return timeSinceLastAttempt < this.config.lockoutDuration;
    }
    
    return false;
  }

  /**
   * Record a failed login attempt
   */
  recordFailedLogin(identifier: string): void {
    const attempts = this.loginAttempts.get(identifier) || { count: 0, lastAttempt: new Date() };
    
    attempts.count++;
    attempts.lastAttempt = new Date();
    
    this.loginAttempts.set(identifier, attempts);
    
    logger.warn('Failed login attempt recorded', { 
      identifier, 
      attemptCount: attempts.count 
    });
  }

  /**
   * Clear failed login attempts for a user
   */
  clearFailedLogins(identifier: string): void {
    this.loginAttempts.delete(identifier);
  }

  /**
   * Log a security event
   */
  logSecurityEvent(event: SecurityEvent): void {
    logger.info('Security event logged', {
      type: event.type,
      userId: event.userId,
      ipAddress: event.ipAddress,
      timestamp: event.timestamp,
      details: event.details,
    });
  }

  /**
   * Generate a secure random token
   */
  generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Validate password strength
   */
  validatePasswordStrength(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }
    
    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Start periodic session cleanup
   */
  private startSessionCleanup(): void {
    setInterval(() => {
      const now = new Date();
      
      for (const [sessionId, session] of this.activeSessions.entries()) {
        const sessionAge = now.getTime() - session.lastActivity.getTime();
        
        if (sessionAge > this.config.sessionTimeout) {
          this.destroySession(sessionId);
        }
      }
    }, 60000); // Run every minute
  }

  /**
   * Get active session count for a user
   */
  getActiveSessionCount(userId: string): number {
    let count = 0;
    
    for (const session of this.activeSessions.values()) {
      if (session.userId === userId && session.isActive) {
        count++;
      }
    }
    
    return count;
  }

  /**
   * Get all active sessions for a user
   */
  getUserSessions(userId: string): UserSession[] {
    const sessions: UserSession[] = [];
    
    for (const session of this.activeSessions.values()) {
      if (session.userId === userId && session.isActive) {
        sessions.push(session);
      }
    }
    
    return sessions;
  }

  /**
   * Destroy all sessions for a user
   */
  destroyAllUserSessions(userId: string): void {
    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (session.userId === userId) {
        this.destroySession(sessionId);
      }
    }
  }
}

export default SecurityService;
