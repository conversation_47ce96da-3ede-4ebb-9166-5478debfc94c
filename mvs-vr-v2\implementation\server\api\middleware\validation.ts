/**
 * Validation Middleware
 *
 * This module provides middleware for validating requests.
 */

import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { Logger } from '../../shared/utils/logger';

// Create logger
const logger = new Logger();

// Define validation schemas
const validationSchemas: Record<string, z.ZodType<any>> = {
  // Bootstrap schemas
  getBootstrapConfig: z.object({
    query: z
      .object({
        environment: z.string().optional(),
        version: z.string().optional(),
      })
      .strict(),
  }),

  // Asset schemas
  getAssetBundle: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
    query: z
      .object({
        version: z.string().optional(),
      })
      .strict(),
  }),

  getAssetMetadata: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
    query: z
      .object({
        version: z.string().optional(),
      })
      .strict(),
  }),

  createAsset: z.object({
    body: z
      .object({
        vendor_id: z.string().uuid(),
        type: z.string(),
        name: z.string().min(1).max(255),
        description: z.string().optional(),
        metadata: z.record(z.any()).optional(),
        tags: z.array(z.string()).optional(),
      })
      .strict(),
  }),

  updateAsset: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
    body: z
      .object({
        name: z.string().min(1).max(255).optional(),
        description: z.string().optional(),
        metadata: z.record(z.any()).optional(),
        tags: z.array(z.string()).optional(),
      })
      .strict(),
  }),

  deleteAsset: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
  }),

  // Scene schemas
  getSceneConfig: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
    query: z
      .object({
        version: z.string().optional(),
      })
      .strict(),
  }),

  createScene: z.object({
    body: z
      .object({
        vendor_id: z.string().uuid(),
        name: z.string().min(1).max(255),
        description: z.string().optional(),
        configuration: z.record(z.any()),
      })
      .strict(),
  }),

  updateScene: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
    body: z
      .object({
        name: z.string().min(1).max(255).optional(),
        description: z.string().optional(),
        configuration: z.record(z.any()).optional(),
      })
      .strict(),
  }),

  deleteScene: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
  }),

  validateScene: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
  }),

  validateScenePerformance: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
  }),

  validateSceneCompatibility: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
    query: z
      .object({
        target_environment: z.string().optional(),
      })
      .strict(),
  }),

  validateSceneData: z.object({
    body: z
      .object({
        data: z.record(z.any()),
      })
      .strict(),
  }),

  validateSceneFlow: z.object({
    body: z
      .object({
        flow: z.record(z.any()),
      })
      .strict(),
  }),

  simulateSceneFlow: z.object({
    body: z
      .object({
        flow: z.record(z.any()),
      })
      .strict(),
  }),

  validateSceneAssets: z.object({
    params: z
      .object({
        scene_id: z.string().uuid(),
      })
      .strict(),
  }),

  validateAsset: z.object({
    params: z
      .object({
        asset_id: z.string().uuid(),
      })
      .strict(),
  }),

  validateAssets: z.object({
    body: z
      .object({
        asset_ids: z.array(z.string().uuid()),
      })
      .strict(),
  }),

  validateSceneBlueprints: z.object({
    params: z
      .object({
        scene_id: z.string().uuid(),
      })
      .strict(),
  }),

  checkBlueprintCompatibility: z.object({
    params: z
      .object({
        scene_id: z.string().uuid(),
      })
      .strict(),
    query: z
      .object({
        target_environment: z.string(),
      })
      .strict(),
  }),

  validateBlueprintInstances: z.object({
    body: z
      .object({
        instances: z.array(
          z.object({
            id: z.string(),
            blueprint_id: z.string().uuid(),
            position: z.object({
              x: z.number(),
              y: z.number(),
              z: z.number(),
            }),
            rotation: z
              .object({
                x: z.number(),
                y: z.number(),
                z: z.number(),
                w: z.number(),
              })
              .optional(),
            scale: z
              .object({
                x: z.number(),
                y: z.number(),
                z: z.number(),
              })
              .optional(),
            properties: z.record(z.any()).optional(),
          }),
        ),
      })
      .strict(),
  }),

  startBackgroundValidation: z.object({
    params: z
      .object({
        scene_id: z.string().uuid(),
      })
      .strict(),
    body: z
      .object({
        job_type: z.enum([
          'validate_scene',
          'analyze_performance',
          'check_compatibility',
          'validate_assets',
          'validate_blueprints',
          'simulate_flow',
          'validate_all',
        ]),
        params: z.record(z.any()).optional(),
        options: z
          .object({
            priority: z.number().min(1).max(100).optional(),
            force: z.boolean().optional(),
            ttl: z.number().min(60).max(86400).optional(),
          })
          .optional(),
      })
      .strict(),
  }),

  getJobStatus: z.object({
    params: z
      .object({
        job_id: z.string(),
      })
      .strict(),
  }),

  validateAll: z.object({
    params: z
      .object({
        scene_id: z.string().uuid(),
      })
      .strict(),
    body: z
      .object({
        options: z
          .object({
            priority: z.number().min(1).max(100).optional(),
            force: z.boolean().optional(),
            ttl: z.number().min(60).max(86400).optional(),
          })
          .optional(),
      })
      .optional(),
  }),

  // Phase Management Schemas
  getScenePhaseState: z.object({
    params: z
      .object({
        scene_id: z.string().uuid(),
      })
      .strict(),
  }),

  initializeScenePhaseState: z.object({
    params: z
      .object({
        scene_id: z.string().uuid(),
      })
      .strict(),
  }),

  transitionToNextPhase: z.object({
    params: z
      .object({
        scene_id: z.string().uuid(),
      })
      .strict(),
  }),

  transitionToPhase: z.object({
    params: z
      .object({
        scene_id: z.string().uuid(),
      })
      .strict(),
    body: z
      .object({
        target_phase: z.enum([
          'planning',
          'asset_creation',
          'scene_construction',
          'interaction_development',
          'optimization',
          'testing',
          'deployment',
        ]),
      })
      .strict(),
  }),

  validatePhase: z.object({
    params: z
      .object({
        scene_id: z.string().uuid(),
      })
      .strict(),
    query: z
      .object({
        phase: z
          .enum([
            'planning',
            'asset_creation',
            'scene_construction',
            'interaction_development',
            'optimization',
            'testing',
            'deployment',
          ])
          .optional(),
        validate: z.literal('true').optional(),
      })
      .strict(),
  }),

  recordValidationResult: z.object({
    params: z
      .object({
        scene_id: z.string().uuid(),
      })
      .strict(),
    body: z
      .object({
        validation_type: z.string(),
        result: z.object({
          valid: z.boolean(),
          details: z.any().optional(),
        }),
      })
      .strict(),
  }),

  skipPhase: z.object({
    params: z
      .object({
        scene_id: z.string().uuid(),
      })
      .strict(),
    body: z
      .object({
        phase: z
          .enum([
            'planning',
            'asset_creation',
            'scene_construction',
            'interaction_development',
            'optimization',
            'testing',
            'deployment',
          ])
          .optional(),
      })
      .strict(),
  }),

  getPhaseProgress: z.object({
    params: z
      .object({
        scene_id: z.string().uuid(),
      })
      .strict(),
  }),

  // Blueprint schemas
  getBlueprintConfig: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
    query: z
      .object({
        version: z.string().optional(),
      })
      .strict(),
  }),

  createBlueprint: z.object({
    body: z
      .object({
        vendor_id: z.string().uuid(),
        name: z.string().min(1).max(255),
        description: z.string().optional(),
        tags: z.array(z.string()).optional(),
        script: z.record(z.any()),
      })
      .strict(),
  }),

  updateBlueprint: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
    body: z
      .object({
        name: z.string().min(1).max(255).optional(),
        description: z.string().optional(),
        tags: z.array(z.string()).optional(),
        script: z.record(z.any()).optional(),
      })
      .strict(),
  }),

  deleteBlueprint: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
  }),

  // User schemas
  registerUser: z.object({
    body: z
      .object({
        email: z.string().email(),
        password: z.string().min(8),
        name: z.string().min(1).max(255),
        metadata: z.record(z.any()).optional(),
      })
      .strict(),
  }),

  loginUser: z.object({
    body: z
      .object({
        email: z.string().email(),
        password: z.string(),
      })
      .strict(),
  }),

  getUserProfile: z.object({
    params: z
      .object({
        id: z.string().uuid().optional(),
      })
      .strict(),
  }),

  updateUserProfile: z.object({
    params: z
      .object({
        id: z.string().uuid().optional(),
      })
      .strict(),
    body: z
      .object({
        name: z.string().min(1).max(255).optional(),
        metadata: z.record(z.any()).optional(),
      })
      .strict(),
  }),

  // Vendor schemas
  registerVendor: z.object({
    body: z
      .object({
        name: z.string().min(1).max(255),
        email: z.string().email(),
        password: z.string().min(8),
        profile: z.record(z.any()),
        subscription_tier: z.enum(['free', 'basic', 'premium', 'enterprise']).optional(),
      })
      .strict(),
  }),

  approveVendor: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
    body: z
      .object({
        approved: z.boolean(),
        message: z.string().optional(),
      })
      .strict(),
  }),

  getVendorProfile: z.object({
    params: z
      .object({
        id: z.string().uuid().optional(),
      })
      .strict(),
  }),

  updateVendorProfile: z.object({
    params: z
      .object({
        id: z.string().uuid().optional(),
      })
      .strict(),
    body: z
      .object({
        name: z.string().min(1).max(255).optional(),
        profile: z.record(z.any()).optional(),
      })
      .strict(),
  }),

  // Order schemas
  createOrder: z.object({
    body: z
      .object({
        user_id: z.string().uuid(),
        vendor_id: z.string().uuid(),
        product_id: z.string().uuid(),
        quantity: z.number().int().positive(),
        metadata: z.record(z.any()).optional(),
      })
      .strict(),
  }),

  getOrder: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
  }),

  updateOrder: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
    body: z
      .object({
        status: z.string().optional(),
        quantity: z.number().int().positive().optional(),
        metadata: z.record(z.any()).optional(),
      })
      .strict(),
  }),

  cancelOrder: z.object({
    params: z
      .object({
        id: z.string().uuid(),
      })
      .strict(),
    body: z
      .object({
        reason: z.string().optional(),
      })
      .strict(),
  }),
};

/**
 * Validate a request against a schema
 *
 * @param schemaName Name of the schema to validate against
 * @returns Middleware function
 */
export const validateRequest = (
  schemaName: string,
): ((req: Request, res: Response, next: NextFunction) => void) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Get the schema
      const schema = validationSchemas[schemaName];

      // Check if the schema exists
      if (!schema) {
        logger.error(`Validation schema ${schemaName} not found`);
        res.status(500).json({
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Validation schema not found',
          },
        });
        return;
      }

      // Validate the request
      schema.parse({
        body: req.body,
        query: req.query,
        params: req.params,
      });

      // Continue to the next middleware
      next();
    } catch (error) {
      // Handle validation errors
      if (error instanceof z.ZodError) {
        logger.warn(`Validation error for schema ${schemaName}`, { error: error.errors });
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request data',
            details: error.errors,
          },
        });
        return;
      }

      // Handle other errors
      logger.error(`Error validating request against schema ${schemaName}`, { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An error occurred while validating the request',
        },
      });
    }
  };
};
