/**
 * Advanced Load Testing Framework
 * 
 * Comprehensive load testing with:
 * - Multiple user scenarios
 * - Real-time monitoring integration
 * - Performance degradation detection
 * - Stress testing with gradual load increase
 * - Resource utilization monitoring
 */

import http from 'k6/http';
import ws from 'k6/ws';
import { check, sleep, group } from 'k6';
import { Rate, Trend, Counter, Gauge } from 'k6/metrics';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/main/dist/bundle.js';
import { textSummary } from 'https://jslib.k6.io/k6-summary/0.0.1/index.js';

// Custom metrics
const errorRate = new Rate('error_rate');
const responseTime = new Trend('response_time');
const apiCalls = new Counter('api_calls');
const activeUsers = new Gauge('active_users');
const wsConnections = new Gauge('websocket_connections');
const assetProcessingTime = new Trend('asset_processing_time');
const dbQueryTime = new Trend('database_query_time');

// Test configuration
const config = {
  baseUrl: __ENV.LOAD_TEST_BASE_URL || 'http://localhost:3000',
  directusUrl: __ENV.LOAD_TEST_DIRECTUS_URL || 'http://localhost:8055',
  wsUrl: __ENV.LOAD_TEST_WS_URL || 'ws://localhost:3000',
  apiKey: __ENV.LOAD_TEST_API_KEY || '',
  
  // Performance thresholds
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests under 2s
    http_req_failed: ['rate<0.05'],    // Error rate under 5%
    error_rate: ['rate<0.05'],
    response_time: ['p(95)<2000'],
    websocket_connections: ['value>0'],
  },
  
  // Load test scenarios
  scenarios: {
    // Smoke test - verify basic functionality
    smoke_test: {
      executor: 'constant-vus',
      vus: 1,
      duration: '1m',
      tags: { test_type: 'smoke' },
    },
    
    // Load test - normal expected load
    load_test: {
      executor: 'ramping-vus',
      startVUs: 5,
      stages: [
        { duration: '2m', target: 20 },  // Ramp up
        { duration: '5m', target: 20 },  // Stay at 20 users
        { duration: '2m', target: 0 },   // Ramp down
      ],
      tags: { test_type: 'load' },
    },
    
    // Stress test - beyond normal capacity
    stress_test: {
      executor: 'ramping-vus',
      startVUs: 20,
      stages: [
        { duration: '2m', target: 50 },  // Ramp up to stress level
        { duration: '5m', target: 50 },  // Maintain stress
        { duration: '2m', target: 100 }, // Peak stress
        { duration: '3m', target: 100 }, // Hold peak
        { duration: '3m', target: 0 },   // Ramp down
      ],
      tags: { test_type: 'stress' },
    },
    
    // Spike test - sudden load increase
    spike_test: {
      executor: 'ramping-vus',
      startVUs: 10,
      stages: [
        { duration: '1m', target: 10 },  // Normal load
        { duration: '30s', target: 100 }, // Spike
        { duration: '1m', target: 100 },  // Hold spike
        { duration: '30s', target: 10 },  // Return to normal
        { duration: '1m', target: 10 },   // Maintain normal
      ],
      tags: { test_type: 'spike' },
    },
    
    // Volume test - large data processing
    volume_test: {
      executor: 'constant-vus',
      vus: 10,
      duration: '10m',
      tags: { test_type: 'volume' },
    },
  },
};

// Test data
const testData = {
  vendors: [
    { email: '<EMAIL>', password: 'TestPass123!' },
    { email: '<EMAIL>', password: 'TestPass123!' },
    { email: '<EMAIL>', password: 'TestPass123!' },
  ],
  clients: [
    { email: '<EMAIL>', password: 'TestPass123!' },
    { email: '<EMAIL>', password: 'TestPass123!' },
    { email: '<EMAIL>', password: 'TestPass123!' },
  ],
  assets: [
    { name: 'Chair Model', type: 'furniture', size: 'large' },
    { name: 'Table Model', type: 'furniture', size: 'medium' },
    { name: 'Lamp Model', type: 'lighting', size: 'small' },
  ],
};

export { config as options };

// Setup function - runs once per VU
export function setup() {
  // Authenticate and get tokens for test users
  const tokens = {};
  
  testData.vendors.forEach((vendor, index) => {
    const response = http.post(`${config.baseUrl}/api/auth/login`, {
      email: vendor.email,
      password: vendor.password,
    });
    
    if (response.status === 200) {
      tokens[`vendor_${index}`] = JSON.parse(response.body).token;
    }
  });
  
  testData.clients.forEach((client, index) => {
    const response = http.post(`${config.baseUrl}/api/auth/login`, {
      email: client.email,
      password: client.password,
    });
    
    if (response.status === 200) {
      tokens[`client_${index}`] = JSON.parse(response.body).token;
    }
  });
  
  return { tokens };
}

// Main test function
export default function (data) {
  const testType = __ENV.TEST_TYPE || 'mixed';
  
  // Update active users metric
  activeUsers.add(1);
  
  group('Authentication Flow', () => {
    testAuthenticationFlow(data);
  });
  
  group('Asset Management', () => {
    testAssetManagement(data);
  });
  
  group('Showroom Operations', () => {
    testShowroomOperations(data);
  });
  
  group('Real-time Features', () => {
    testRealTimeFeatures(data);
  });
  
  group('Visual Editors', () => {
    testVisualEditors(data);
  });
  
  // Random sleep between 1-3 seconds to simulate user think time
  sleep(Math.random() * 2 + 1);
}

function testAuthenticationFlow(data) {
  const startTime = Date.now();
  
  // Test login
  const loginResponse = http.post(`${config.baseUrl}/api/auth/login`, {
    email: testData.vendors[0].email,
    password: testData.vendors[0].password,
  });
  
  const loginSuccess = check(loginResponse, {
    'login status is 200': (r) => r.status === 200,
    'login response time < 1s': (r) => r.timings.duration < 1000,
    'login returns token': (r) => JSON.parse(r.body).token !== undefined,
  });
  
  errorRate.add(!loginSuccess);
  responseTime.add(loginResponse.timings.duration);
  apiCalls.add(1);
  
  if (loginSuccess) {
    const token = JSON.parse(loginResponse.body).token;
    
    // Test protected endpoint
    const profileResponse = http.get(`${config.baseUrl}/api/auth/profile`, {
      headers: { Authorization: `Bearer ${token}` },
    });
    
    check(profileResponse, {
      'profile status is 200': (r) => r.status === 200,
      'profile response time < 500ms': (r) => r.timings.duration < 500,
    });
    
    responseTime.add(profileResponse.timings.duration);
    apiCalls.add(1);
  }
}

function testAssetManagement(data) {
  const token = data.tokens.vendor_0;
  if (!token) return;
  
  // List assets
  const listResponse = http.get(`${config.baseUrl}/api/assets`, {
    headers: { Authorization: `Bearer ${token}` },
  });
  
  check(listResponse, {
    'assets list status is 200': (r) => r.status === 200,
    'assets list response time < 1s': (r) => r.timings.duration < 1000,
  });
  
  responseTime.add(listResponse.timings.duration);
  apiCalls.add(1);
  
  // Simulate asset processing
  const processingStart = Date.now();
  const processResponse = http.post(`${config.baseUrl}/api/assets/process`, {
    assetId: 'test-asset-id',
    options: { optimize: true, generateThumbnails: true },
  }, {
    headers: { Authorization: `Bearer ${token}` },
  });
  
  const processingTime = Date.now() - processingStart;
  assetProcessingTime.add(processingTime);
  
  check(processResponse, {
    'asset processing initiated': (r) => r.status === 202,
    'processing response time < 2s': (r) => r.timings.duration < 2000,
  });
  
  apiCalls.add(1);
}

function testShowroomOperations(data) {
  const token = data.tokens.vendor_0;
  if (!token) return;
  
  // Create showroom
  const createResponse = http.post(`${config.baseUrl}/api/showrooms`, {
    name: `Load Test Showroom ${Math.random()}`,
    description: 'Generated during load testing',
    category: 'furniture',
  }, {
    headers: { Authorization: `Bearer ${token}` },
  });
  
  check(createResponse, {
    'showroom creation status is 201': (r) => r.status === 201,
    'showroom creation time < 1.5s': (r) => r.timings.duration < 1500,
  });
  
  responseTime.add(createResponse.timings.duration);
  apiCalls.add(1);
  
  if (createResponse.status === 201) {
    const showroomId = JSON.parse(createResponse.body).id;
    
    // Update showroom
    const updateResponse = http.patch(`${config.baseUrl}/api/showrooms/${showroomId}`, {
      description: 'Updated during load test',
    }, {
      headers: { Authorization: `Bearer ${token}` },
    });
    
    check(updateResponse, {
      'showroom update status is 200': (r) => r.status === 200,
    });
    
    apiCalls.add(1);
  }
}

function testRealTimeFeatures(data) {
  const token = data.tokens.vendor_0;
  if (!token) return;
  
  // Test WebSocket connection
  const wsResponse = ws.connect(`${config.wsUrl}/ws`, {
    headers: { Authorization: `Bearer ${token}` },
  }, function (socket) {
    wsConnections.add(1);
    
    socket.on('open', () => {
      socket.send(JSON.stringify({
        type: 'subscribe',
        channel: 'showroom_updates',
      }));
    });
    
    socket.on('message', (data) => {
      const message = JSON.parse(data);
      check(message, {
        'websocket message is valid': (msg) => msg.type !== undefined,
      });
    });
    
    socket.setTimeout(() => {
      socket.close();
      wsConnections.add(-1);
    }, 5000);
  });
}

function testVisualEditors(data) {
  const token = data.tokens.vendor_0;
  if (!token) return;
  
  // Test layout editor
  const layoutResponse = http.post(`${config.baseUrl}/api/visual-editors/layout`, {
    showroomId: 'test-showroom',
    layout: {
      type: 'grid',
      items: [
        { id: 'asset1', position: { x: 0, y: 0 } },
        { id: 'asset2', position: { x: 1, y: 0 } },
      ],
    },
  }, {
    headers: { Authorization: `Bearer ${token}` },
  });
  
  check(layoutResponse, {
    'layout editor status is 200': (r) => r.status === 200,
    'layout editor response time < 1s': (r) => r.timings.duration < 1000,
  });
  
  responseTime.add(layoutResponse.timings.duration);
  apiCalls.add(1);
}

// Teardown function
export function teardown(data) {
  console.log('Load test completed');
  console.log(`Total API calls: ${apiCalls.count}`);
  console.log(`Average response time: ${responseTime.avg}ms`);
  console.log(`Error rate: ${(errorRate.rate * 100).toFixed(2)}%`);
}

// Custom summary report
export function handleSummary(data) {
  return {
    'load-test-report.html': htmlReport(data),
    'load-test-summary.json': JSON.stringify(data, null, 2),
    stdout: textSummary(data, { indent: ' ', enableColors: true }),
  };
}
