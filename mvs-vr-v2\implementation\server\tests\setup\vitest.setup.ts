import { beforeAll, afterAll, vi, expect, type SpyInstance } from 'vitest';
import { config } from '@vue/test-utils';
import '@testing-library/jest-dom';
import { setupRedisMocks, resetGlobalRedisMock } from './redis-mock-setup.js';

// Custom matchers for better test assertions
declare global {
  interface Console {
    log: SpyInstance;
    error: SpyInstance;
    warn: SpyInstance;
    info: SpyInstance;
    debug: SpyInstance;
  }

  namespace Vi {
    interface Assertion {
      toBeInTheDocument(): void;
      toBeVisible(): void;
      toBeEmpty(): void;
      toHaveAttribute(attr: string, value?: string): void;
      toHaveStyle(style: Record<string, any>): void;
      toHaveClass(...classNames: string[]): void;
    }
  }
}

// Configure Vue Test Utils
config.global.stubs = {
  transition: false,
  'router-link': true,
};

// Mock logger for all tests
vi.mock('../../utils/logger.js', () => ({
  default: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
    trace: vi.fn(),
  },
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
    trace: vi.fn(),
  },
}));

// Setup Redis mocks and console mocks for testing
beforeAll(() => {
  // Setup comprehensive Redis mocks
  setupRedisMocks();

  const mockConsole = {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn(),
    dir: vi.fn(),
    dirxml: vi.fn(),
    table: vi.fn(),
    trace: vi.fn(),
    group: vi.fn(),
    groupCollapsed: vi.fn(),
    groupEnd: vi.fn(),
    clear: vi.fn(),
    count: vi.fn(),
    countReset: vi.fn(),
    assert: vi.fn(),
    profile: vi.fn(),
    profileEnd: vi.fn(),
    time: vi.fn(),
    timeLog: vi.fn(),
    timeEnd: vi.fn(),
    timeStamp: vi.fn(),
    context: vi.fn(),
  };

  Object.assign(global.console, mockConsole);
});

// Clean up mocks after tests
afterAll(() => {
  vi.clearAllMocks();
  resetGlobalRedisMock();
});

// Add custom matchers
expect.extend({
  toBeInTheDocument(received: Element) {
    const pass = document.body.contains(received);
    return {
      pass,
      message: () => `expected element to${pass ? ' not' : ''} be in the document`,
    };
  },
  toBeVisible(received: Element) {
    const computedStyle = window.getComputedStyle(received);
    const visibility = computedStyle.getPropertyValue('visibility');
    const display = computedStyle.getPropertyValue('display');
    const opacity = computedStyle.getPropertyValue('opacity');
    const pass = visibility !== 'hidden' && display !== 'none' && opacity !== '0';
    return {
      pass,
      message: () => `expected element to${pass ? ' not' : ''} be visible`,
    };
  },
});
