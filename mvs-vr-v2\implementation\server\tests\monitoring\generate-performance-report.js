#!/usr/bin/env node

/**
 * Performance Report Generator
 * 
 * Generates comprehensive performance reports from test results:
 * - Test execution summary
 * - Performance metrics analysis
 * - Coverage reports
 * - Trend analysis
 * - Recommendations
 */

const fs = require('fs').promises;
const path = require('path');

class PerformanceReportGenerator {
  constructor(options = {}) {
    this.config = {
      artifactsPath: options.artifactsPath || './test-artifacts',
      outputFormat: options.outputFormat || 'markdown',
      includeCharts: options.includeCharts || false,
      ...options,
    };
    
    this.data = {
      summary: {},
      coverage: {},
      performance: {},
      trends: {},
      recommendations: [],
    };
  }

  async generate() {
    console.log('📊 Generating performance report...');
    
    try {
      await this.loadData();
      await this.analyzeData();
      await this.generateRecommendations();
      
      const report = await this.formatReport();
      
      console.log('✅ Performance report generated');
      return report;
    } catch (error) {
      console.error('❌ Report generation failed:', error);
      throw error;
    }
  }

  async loadData() {
    console.log('📂 Loading test data...');
    
    const artifactsPath = this.config.artifactsPath;
    
    // Load test summaries
    try {
      const files = await this.findFiles(artifactsPath, '*test-results*.json');
      for (const file of files) {
        const data = JSON.parse(await fs.readFile(file, 'utf8'));
        const testType = this.extractTestType(file);
        this.data.summary[testType] = this.parseTestResults(data);
      }
    } catch (error) {
      console.warn('⚠️ Could not load test summaries:', error.message);
    }

    // Load coverage data
    try {
      const coverageFiles = await this.findFiles(artifactsPath, 'coverage-summary*.json');
      for (const file of coverageFiles) {
        const data = JSON.parse(await fs.readFile(file, 'utf8'));
        this.data.coverage = this.parseCoverageData(data);
      }
    } catch (error) {
      console.warn('⚠️ Could not load coverage data:', error.message);
    }

    // Load performance data
    try {
      const perfFiles = await this.findFiles(artifactsPath, 'load-test-results*.json');
      for (const file of perfFiles) {
        const data = JSON.parse(await fs.readFile(file, 'utf8'));
        this.data.performance = this.parsePerformanceData(data);
      }
    } catch (error) {
      console.warn('⚠️ Could not load performance data:', error.message);
    }
  }

  async findFiles(dir, pattern) {
    const files = [];
    
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
          const subFiles = await this.findFiles(fullPath, pattern);
          files.push(...subFiles);
        } else if (entry.isFile() && this.matchesPattern(entry.name, pattern)) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // Directory doesn't exist or can't be read
    }
    
    return files;
  }

  matchesPattern(filename, pattern) {
    const regex = new RegExp(pattern.replace('*', '.*'));
    return regex.test(filename);
  }

  extractTestType(filepath) {
    const filename = path.basename(filepath);
    if (filename.includes('unit')) return 'unit';
    if (filename.includes('integration')) return 'integration';
    if (filename.includes('e2e')) return 'e2e';
    if (filename.includes('load')) return 'load';
    return 'unknown';
  }

  parseTestResults(data) {
    if (data.testResults) {
      return {
        total: data.numTotalTests || 0,
        passed: data.numPassedTests || 0,
        failed: data.numFailedTests || 0,
        skipped: data.numPendingTests || 0,
        duration: data.testResults.reduce((sum, result) => sum + (result.perfStats?.runtime || 0), 0),
        success: data.success,
      };
    } else if (data.stats) {
      return {
        total: data.stats.tests || 0,
        passed: data.stats.passes || 0,
        failed: data.stats.failures || 0,
        skipped: data.stats.pending || 0,
        duration: data.stats.duration || 0,
        success: data.stats.failures === 0,
      };
    }
    
    return {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
      success: false,
    };
  }

  parseCoverageData(data) {
    if (data.total) {
      return {
        lines: data.total.lines?.pct || 0,
        functions: data.total.functions?.pct || 0,
        branches: data.total.branches?.pct || 0,
        statements: data.total.statements?.pct || 0,
        uncoveredLines: data.total.lines?.uncovered || 0,
      };
    }
    
    return {};
  }

  parsePerformanceData(data) {
    if (data.metrics) {
      return {
        avgResponseTime: data.metrics.http_req_duration?.avg || 0,
        p95ResponseTime: data.metrics.http_req_duration?.p95 || 0,
        errorRate: data.metrics.http_req_failed?.rate || 0,
        throughput: data.metrics.http_reqs?.rate || 0,
        iterations: data.metrics.iterations?.count || 0,
        vus: data.metrics.vus?.value || 0,
      };
    }
    
    return {};
  }

  async analyzeData() {
    console.log('🔍 Analyzing data...');
    
    // Calculate overall test health
    const summary = this.data.summary;
    const totalTests = Object.values(summary).reduce((sum, s) => sum + s.total, 0);
    const totalPassed = Object.values(summary).reduce((sum, s) => sum + s.passed, 0);
    const totalFailed = Object.values(summary).reduce((sum, s) => sum + s.failed, 0);
    
    this.data.overall = {
      totalTests,
      totalPassed,
      totalFailed,
      successRate: totalTests > 0 ? (totalPassed / totalTests) * 100 : 0,
      overallHealth: this.calculateOverallHealth(),
    };
  }

  calculateOverallHealth() {
    const summary = this.data.summary;
    const coverage = this.data.coverage;
    const performance = this.data.performance;
    
    let score = 100;
    
    // Deduct points for test failures
    Object.values(summary).forEach(s => {
      if (s.total > 0) {
        const failureRate = (s.failed / s.total) * 100;
        score -= failureRate * 2; // 2 points per % failure rate
      }
    });
    
    // Deduct points for low coverage
    if (coverage.lines < 70) {
      score -= (70 - coverage.lines) * 0.5;
    }
    
    // Deduct points for poor performance
    if (performance.avgResponseTime > 1000) {
      score -= (performance.avgResponseTime - 1000) / 100;
    }
    
    if (performance.errorRate > 0.01) {
      score -= performance.errorRate * 1000;
    }
    
    return Math.max(0, Math.min(100, score));
  }

  async generateRecommendations() {
    console.log('💡 Generating recommendations...');
    
    const summary = this.data.summary;
    const coverage = this.data.coverage;
    const performance = this.data.performance;
    
    // Test failure recommendations
    Object.entries(summary).forEach(([type, s]) => {
      if (s.failed > 0) {
        this.data.recommendations.push({
          type: 'test_failures',
          priority: 'high',
          message: `Fix ${s.failed} failing ${type} test(s) to improve reliability`,
          action: `Review and fix failing ${type} tests`,
        });
      }
    });
    
    // Coverage recommendations
    if (coverage.lines < 80) {
      this.data.recommendations.push({
        type: 'coverage',
        priority: coverage.lines < 60 ? 'high' : 'medium',
        message: `Increase test coverage from ${coverage.lines}% to at least 80%`,
        action: 'Add tests for uncovered code paths',
      });
    }
    
    // Performance recommendations
    if (performance.avgResponseTime > 1000) {
      this.data.recommendations.push({
        type: 'performance',
        priority: 'medium',
        message: `Improve API response time (currently ${performance.avgResponseTime}ms)`,
        action: 'Optimize slow endpoints and database queries',
      });
    }
    
    if (performance.errorRate > 0.05) {
      this.data.recommendations.push({
        type: 'reliability',
        priority: 'high',
        message: `Reduce error rate from ${(performance.errorRate * 100).toFixed(2)}% to under 1%`,
        action: 'Investigate and fix error-prone endpoints',
      });
    }
  }

  async formatReport() {
    if (this.config.outputFormat === 'markdown') {
      return this.generateMarkdownReport();
    } else if (this.config.outputFormat === 'json') {
      return JSON.stringify(this.data, null, 2);
    } else {
      throw new Error(`Unsupported output format: ${this.config.outputFormat}`);
    }
  }

  generateMarkdownReport() {
    const { summary, coverage, performance, overall, recommendations } = this.data;
    
    let report = '';
    
    // Header
    report += '# 🧪 Test Performance Report\n\n';
    report += `Generated on: ${new Date().toISOString()}\n\n`;
    
    // Overall Health
    const healthEmoji = overall.overallHealth >= 90 ? '🟢' : overall.overallHealth >= 70 ? '🟡' : '🔴';
    report += `## ${healthEmoji} Overall Health Score: ${overall.overallHealth.toFixed(1)}/100\n\n`;
    
    // Test Summary
    report += '## 📊 Test Summary\n\n';
    report += '| Test Type | Total | Passed | Failed | Success Rate | Duration |\n';
    report += '|-----------|-------|--------|--------|--------------|----------|\n';
    
    Object.entries(summary).forEach(([type, s]) => {
      const successRate = s.total > 0 ? ((s.passed / s.total) * 100).toFixed(1) : '0';
      const emoji = s.success ? '✅' : '❌';
      report += `| ${emoji} ${type} | ${s.total} | ${s.passed} | ${s.failed} | ${successRate}% | ${s.duration}ms |\n`;
    });
    
    report += '\n';
    
    // Coverage Report
    if (Object.keys(coverage).length > 0) {
      report += '## 📈 Coverage Report\n\n';
      report += '| Metric | Coverage |\n';
      report += '|--------|----------|\n';
      report += `| Lines | ${coverage.lines}% |\n`;
      report += `| Functions | ${coverage.functions}% |\n`;
      report += `| Branches | ${coverage.branches}% |\n`;
      report += `| Statements | ${coverage.statements}% |\n`;
      report += '\n';
    }
    
    // Performance Report
    if (Object.keys(performance).length > 0) {
      report += '## ⚡ Performance Report\n\n';
      report += '| Metric | Value |\n';
      report += '|--------|-------|\n';
      report += `| Average Response Time | ${performance.avgResponseTime}ms |\n`;
      report += `| 95th Percentile | ${performance.p95ResponseTime}ms |\n`;
      report += `| Error Rate | ${(performance.errorRate * 100).toFixed(2)}% |\n`;
      report += `| Throughput | ${performance.throughput.toFixed(2)} req/s |\n`;
      report += `| Virtual Users | ${performance.vus} |\n`;
      report += '\n';
    }
    
    // Recommendations
    if (recommendations.length > 0) {
      report += '## 💡 Recommendations\n\n';
      
      const highPriority = recommendations.filter(r => r.priority === 'high');
      const mediumPriority = recommendations.filter(r => r.priority === 'medium');
      const lowPriority = recommendations.filter(r => r.priority === 'low');
      
      if (highPriority.length > 0) {
        report += '### 🚨 High Priority\n\n';
        highPriority.forEach(r => {
          report += `- **${r.message}**\n  - Action: ${r.action}\n\n`;
        });
      }
      
      if (mediumPriority.length > 0) {
        report += '### ⚠️ Medium Priority\n\n';
        mediumPriority.forEach(r => {
          report += `- **${r.message}**\n  - Action: ${r.action}\n\n`;
        });
      }
      
      if (lowPriority.length > 0) {
        report += '### ℹ️ Low Priority\n\n';
        lowPriority.forEach(r => {
          report += `- **${r.message}**\n  - Action: ${r.action}\n\n`;
        });
      }
    }
    
    // Footer
    report += '---\n\n';
    report += '*Report generated by MVS-VR Test Performance Analyzer*\n';
    
    return report;
  }
}

// CLI execution
if (require.main === module) {
  const artifactsPath = process.argv[2] || './test-artifacts';
  const outputFormat = process.argv[3] || 'markdown';
  
  const generator = new PerformanceReportGenerator({ 
    artifactsPath, 
    outputFormat 
  });
  
  generator.generate()
    .then(report => {
      console.log(report);
    })
    .catch(error => {
      console.error('❌ Report generation failed:', error);
      process.exit(1);
    });
}

module.exports = PerformanceReportGenerator;
