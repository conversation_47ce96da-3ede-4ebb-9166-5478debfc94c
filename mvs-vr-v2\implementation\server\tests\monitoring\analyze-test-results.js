#!/usr/bin/env node

/**
 * Test Results Analyzer
 * 
 * Analyzes test results from CI/CD pipeline and generates insights:
 * - Performance regression detection
 * - Test failure pattern analysis
 * - Coverage trend analysis
 * - Alert generation for critical issues
 */

const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');

class TestResultsAnalyzer {
  constructor(options = {}) {
    this.config = {
      artifactsPath: options.artifactsPath || './test-artifacts',
      slackWebhook: process.env.SLACK_WEBHOOK,
      githubToken: process.env.GITHUB_TOKEN,
      thresholds: {
        coverageThreshold: 70,
        performanceRegressionThreshold: 20,
        failureRateThreshold: 5,
        ...options.thresholds,
      },
    };
    
    this.results = {
      summary: {},
      coverage: {},
      performance: {},
      failures: [],
      alerts: [],
    };
  }

  async analyze() {
    console.log('🔍 Analyzing test results...');
    
    try {
      await this.loadTestResults();
      await this.analyzeCoverage();
      await this.analyzePerformance();
      await this.analyzeFailures();
      await this.generateAlerts();
      await this.sendNotifications();
      
      console.log('✅ Analysis complete');
      return this.results;
    } catch (error) {
      console.error('❌ Analysis failed:', error);
      throw error;
    }
  }

  async loadTestResults() {
    console.log('📂 Loading test results...');
    
    const artifactsPath = this.config.artifactsPath;
    
    // Load unit test results
    try {
      const unitTestFiles = await this.findFiles(artifactsPath, 'unit-test-results*.json');
      for (const file of unitTestFiles) {
        const data = JSON.parse(await fs.readFile(file, 'utf8'));
        this.results.summary.unit = this.parseTestResults(data);
      }
    } catch (error) {
      console.warn('⚠️ Could not load unit test results:', error.message);
    }

    // Load integration test results
    try {
      const integrationTestFiles = await this.findFiles(artifactsPath, 'integration-test-results*.json');
      for (const file of integrationTestFiles) {
        const data = JSON.parse(await fs.readFile(file, 'utf8'));
        this.results.summary.integration = this.parseTestResults(data);
      }
    } catch (error) {
      console.warn('⚠️ Could not load integration test results:', error.message);
    }

    // Load E2E test results
    try {
      const e2eTestFiles = await this.findFiles(artifactsPath, 'e2e-test-results*.json');
      for (const file of e2eTestFiles) {
        const data = JSON.parse(await fs.readFile(file, 'utf8'));
        this.results.summary.e2e = this.parseTestResults(data);
      }
    } catch (error) {
      console.warn('⚠️ Could not load E2E test results:', error.message);
    }

    // Load load test results
    try {
      const loadTestFiles = await this.findFiles(artifactsPath, 'load-test-results*.json');
      for (const file of loadTestFiles) {
        const data = JSON.parse(await fs.readFile(file, 'utf8'));
        this.results.performance.load = this.parseLoadTestResults(data);
      }
    } catch (error) {
      console.warn('⚠️ Could not load load test results:', error.message);
    }

    // Load coverage results
    try {
      const coverageFiles = await this.findFiles(artifactsPath, 'coverage-summary*.json');
      for (const file of coverageFiles) {
        const data = JSON.parse(await fs.readFile(file, 'utf8'));
        this.results.coverage = this.parseCoverageResults(data);
      }
    } catch (error) {
      console.warn('⚠️ Could not load coverage results:', error.message);
    }
  }

  async findFiles(dir, pattern) {
    const files = [];
    
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
          const subFiles = await this.findFiles(fullPath, pattern);
          files.push(...subFiles);
        } else if (entry.isFile() && this.matchesPattern(entry.name, pattern)) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // Directory doesn't exist or can't be read
    }
    
    return files;
  }

  matchesPattern(filename, pattern) {
    const regex = new RegExp(pattern.replace('*', '.*'));
    return regex.test(filename);
  }

  parseTestResults(data) {
    // Handle different test result formats
    if (data.testResults) {
      // Jest/Vitest format
      return {
        total: data.numTotalTests || 0,
        passed: data.numPassedTests || 0,
        failed: data.numFailedTests || 0,
        skipped: data.numPendingTests || 0,
        duration: data.testResults.reduce((sum, result) => sum + (result.perfStats?.runtime || 0), 0),
        success: data.success,
      };
    } else if (data.stats) {
      // Mocha format
      return {
        total: data.stats.tests || 0,
        passed: data.stats.passes || 0,
        failed: data.stats.failures || 0,
        skipped: data.stats.pending || 0,
        duration: data.stats.duration || 0,
        success: data.stats.failures === 0,
      };
    }
    
    return {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
      success: false,
    };
  }

  parseLoadTestResults(data) {
    if (data.metrics) {
      return {
        httpReqDuration: data.metrics.http_req_duration?.avg || 0,
        httpReqFailed: data.metrics.http_req_failed?.rate || 0,
        iterations: data.metrics.iterations?.count || 0,
        vus: data.metrics.vus?.value || 0,
        dataReceived: data.metrics.data_received?.count || 0,
        dataSent: data.metrics.data_sent?.count || 0,
      };
    }
    
    return {};
  }

  parseCoverageResults(data) {
    if (data.total) {
      return {
        lines: data.total.lines?.pct || 0,
        functions: data.total.functions?.pct || 0,
        branches: data.total.branches?.pct || 0,
        statements: data.total.statements?.pct || 0,
      };
    }
    
    return {};
  }

  async analyzeCoverage() {
    console.log('📊 Analyzing coverage...');
    
    const coverage = this.results.coverage;
    
    if (coverage.lines < this.config.thresholds.coverageThreshold) {
      this.results.alerts.push({
        type: 'low_coverage',
        severity: 'warning',
        message: `Line coverage (${coverage.lines}%) is below threshold (${this.config.thresholds.coverageThreshold}%)`,
        data: coverage,
      });
    }
  }

  async analyzePerformance() {
    console.log('⚡ Analyzing performance...');
    
    const performance = this.results.performance;
    
    if (performance.load) {
      // Check for performance regressions
      if (performance.load.httpReqDuration > 2000) { // 2 seconds
        this.results.alerts.push({
          type: 'performance_regression',
          severity: 'warning',
          message: `Average response time (${performance.load.httpReqDuration}ms) exceeds acceptable threshold`,
          data: performance.load,
        });
      }
      
      if (performance.load.httpReqFailed > 0.05) { // 5% error rate
        this.results.alerts.push({
          type: 'high_error_rate',
          severity: 'critical',
          message: `HTTP error rate (${(performance.load.httpReqFailed * 100).toFixed(2)}%) is too high`,
          data: performance.load,
        });
      }
    }
  }

  async analyzeFailures() {
    console.log('🔍 Analyzing failures...');
    
    const summary = this.results.summary;
    
    Object.entries(summary).forEach(([testType, results]) => {
      if (results.failed > 0) {
        const failureRate = (results.failed / results.total) * 100;
        
        if (failureRate > this.config.thresholds.failureRateThreshold) {
          this.results.alerts.push({
            type: 'high_failure_rate',
            severity: 'critical',
            message: `${testType} tests have high failure rate: ${failureRate.toFixed(2)}% (${results.failed}/${results.total})`,
            data: results,
          });
        }
      }
    });
  }

  async generateAlerts() {
    console.log('🚨 Generating alerts...');
    
    // Sort alerts by severity
    this.results.alerts.sort((a, b) => {
      const severityOrder = { critical: 3, warning: 2, info: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
  }

  async sendNotifications() {
    console.log('📢 Sending notifications...');
    
    if (this.results.alerts.length === 0) {
      console.log('✅ No alerts to send');
      return;
    }

    // Send to Slack
    if (this.config.slackWebhook) {
      await this.sendSlackNotification();
    }

    // Log alerts to console
    this.results.alerts.forEach(alert => {
      const emoji = alert.severity === 'critical' ? '🚨' : '⚠️';
      console.log(`${emoji} ${alert.type}: ${alert.message}`);
    });
  }

  async sendSlackNotification() {
    const criticalAlerts = this.results.alerts.filter(a => a.severity === 'critical');
    const warningAlerts = this.results.alerts.filter(a => a.severity === 'warning');
    
    let message = '🧪 *Test Results Summary*\n\n';
    
    if (criticalAlerts.length > 0) {
      message += '🚨 *Critical Issues:*\n';
      criticalAlerts.forEach(alert => {
        message += `• ${alert.message}\n`;
      });
      message += '\n';
    }
    
    if (warningAlerts.length > 0) {
      message += '⚠️ *Warnings:*\n';
      warningAlerts.forEach(alert => {
        message += `• ${alert.message}\n`;
      });
      message += '\n';
    }
    
    // Add summary
    const summary = this.results.summary;
    if (Object.keys(summary).length > 0) {
      message += '📊 *Test Summary:*\n';
      Object.entries(summary).forEach(([type, results]) => {
        const emoji = results.success ? '✅' : '❌';
        message += `${emoji} ${type}: ${results.passed}/${results.total} passed\n`;
      });
    }

    try {
      await axios.post(this.config.slackWebhook, {
        text: message,
        channel: '#test-results',
        username: 'Test Analyzer',
        icon_emoji: ':test_tube:',
      });
      
      console.log('✅ Slack notification sent');
    } catch (error) {
      console.error('❌ Failed to send Slack notification:', error.message);
    }
  }
}

// CLI execution
if (require.main === module) {
  const artifactsPath = process.argv[2] || './test-artifacts';
  
  const analyzer = new TestResultsAnalyzer({ artifactsPath });
  
  analyzer.analyze()
    .then(results => {
      console.log('\n📋 Analysis Results:');
      console.log(JSON.stringify(results, null, 2));
      
      // Exit with error code if there are critical alerts
      const criticalAlerts = results.alerts.filter(a => a.severity === 'critical');
      if (criticalAlerts.length > 0) {
        console.log(`\n❌ ${criticalAlerts.length} critical issue(s) found`);
        process.exit(1);
      } else {
        console.log('\n✅ No critical issues found');
        process.exit(0);
      }
    })
    .catch(error => {
      console.error('❌ Analysis failed:', error);
      process.exit(1);
    });
}

module.exports = TestResultsAnalyzer;
