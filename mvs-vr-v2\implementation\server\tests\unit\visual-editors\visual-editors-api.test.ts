/**
 * Visual Editors API Integration Tests
 *
 * Tests the API layer for Visual Editors functionality without Vue components
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  createMockApiClient,
  generateMockVendor,
  generateMockProduct,
  generateMockMaterial,
  generateMockAnimation,
  generateMockShowroom,
  generateMockLightingSetup,
} from './test-utils';

// Mock API service for Visual Editors
class VisualEditorsApiService {
  constructor(private apiClient: any) {}

  // Vendor operations
  async getVendorData(vendorId: string) {
    const response = await this.apiClient.get(`/items/vendors/${vendorId}`);
    return response.data;
  }

  // Product operations
  async getProducts(vendorId: string) {
    const response = await this.apiClient.get(`/items/products?filter[vendor_id][_eq]=${vendorId}`);
    return response.data;
  }

  async saveProduct(product: any) {
    if (product.id) {
      const response = await this.apiClient.patch(`/items/products/${product.id}`, product);
      return response.data;
    } else {
      const response = await this.apiClient.post('/items/products', product);
      return response.data;
    }
  }

  // Material operations
  async getMaterials(vendorId: string) {
    const response = await this.apiClient.get(
      `/items/materials?filter[vendor_id][_eq]=${vendorId}`,
    );
    return response.data;
  }

  async saveMaterial(material: any) {
    if (material.id) {
      const response = await this.apiClient.patch(`/items/materials/${material.id}`, material);
      return response.data;
    } else {
      const response = await this.apiClient.post('/items/materials', material);
      return response.data;
    }
  }

  // Animation operations
  async getAnimations(vendorId: string) {
    const response = await this.apiClient.get(
      `/items/animations?filter[vendor_id][_eq]=${vendorId}`,
    );
    return response.data;
  }

  async saveAnimation(animation: any) {
    if (animation.id) {
      const response = await this.apiClient.patch(`/items/animations/${animation.id}`, animation);
      return response.data;
    } else {
      const response = await this.apiClient.post('/items/animations', animation);
      return response.data;
    }
  }

  // Showroom operations
  async getShowrooms(vendorId: string) {
    const response = await this.apiClient.get(
      `/items/showroom_layouts?filter[vendor_id][_eq]=${vendorId}`,
    );
    return response.data;
  }

  async saveShowroom(showroom: any) {
    if (showroom.id) {
      const response = await this.apiClient.patch(
        `/items/showroom_layouts/${showroom.id}`,
        showroom,
      );
      return response.data;
    } else {
      const response = await this.apiClient.post('/items/showroom_layouts', showroom);
      return response.data;
    }
  }

  // Lighting operations
  async getLightingSetup(showroomId: string) {
    const response = await this.apiClient.get(
      `/items/showroom_lighting?filter[showroom_id][_eq]=${showroomId}`,
    );
    return response.data;
  }

  async saveLightingSetup(lighting: any) {
    if (lighting.id) {
      const response = await this.apiClient.patch(
        `/items/showroom_lighting/${lighting.id}`,
        lighting,
      );
      return response.data;
    } else {
      const response = await this.apiClient.post('/items/showroom_lighting', lighting);
      return response.data;
    }
  }

  // File upload operations
  async uploadFile(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    const response = await this.apiClient.post('/files', formData);
    return response.data;
  }

  // Validation operations
  validateProduct(product: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!product.name || product.name.trim() === '') {
      errors.push('Product name is required');
    }

    if (!product.vendor_id) {
      errors.push('Vendor ID is required');
    }

    if (!product.category) {
      errors.push('Product category is required');
    }

    return { valid: errors.length === 0, errors };
  }

  validateMaterial(material: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!material.name || material.name.trim() === '') {
      errors.push('Material name is required');
    }

    if (!material.vendor_id) {
      errors.push('Vendor ID is required');
    }

    if (!material.type) {
      errors.push('Material type is required');
    }

    if (material.properties) {
      if (
        typeof material.properties.roughness !== 'number' ||
        material.properties.roughness < 0 ||
        material.properties.roughness > 1
      ) {
        errors.push('Roughness must be a number between 0 and 1');
      }

      if (
        typeof material.properties.metallic !== 'number' ||
        material.properties.metallic < 0 ||
        material.properties.metallic > 1
      ) {
        errors.push('Metallic must be a number between 0 and 1');
      }
    }

    return { valid: errors.length === 0, errors };
  }

  validateAnimation(animation: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!animation.name || animation.name.trim() === '') {
      errors.push('Animation name is required');
    }

    if (!animation.vendor_id) {
      errors.push('Vendor ID is required');
    }

    if (!animation.target_object) {
      errors.push('Target object is required');
    }

    if (typeof animation.duration !== 'number' || animation.duration <= 0) {
      errors.push('Duration must be a positive number');
    }

    if (!Array.isArray(animation.keyframes) || animation.keyframes.length === 0) {
      errors.push('At least one keyframe is required');
    }

    return { valid: errors.length === 0, errors };
  }
}

describe('Visual Editors API Service', () => {
  let apiService: VisualEditorsApiService;
  let mockApiClient: any;

  beforeEach(() => {
    mockApiClient = createMockApiClient();
    apiService = new VisualEditorsApiService(mockApiClient);
    vi.clearAllMocks();
  });

  describe('Vendor Operations', () => {
    it('should fetch vendor data', async () => {
      const mockVendor = generateMockVendor();
      mockApiClient.get.mockResolvedValue({ data: mockVendor });

      const result = await apiService.getVendorData('vendor-1');

      expect(mockApiClient.get).toHaveBeenCalledWith('/items/vendors/vendor-1');
      expect(result).toEqual(mockVendor);
    });
  });

  describe('Product Operations', () => {
    it('should fetch products for a vendor', async () => {
      const mockProducts = [generateMockProduct(), generateMockProduct({ id: 'product-2' })];
      mockApiClient.get.mockResolvedValue({ data: mockProducts });

      const result = await apiService.getProducts('vendor-1');

      expect(mockApiClient.get).toHaveBeenCalledWith(
        '/items/products?filter[vendor_id][_eq]=vendor-1',
      );
      expect(result).toEqual(mockProducts);
    });

    it('should save new product', async () => {
      const newProduct = generateMockProduct({ id: null });
      const savedProduct = generateMockProduct();
      mockApiClient.post.mockResolvedValue({ data: savedProduct });

      const result = await apiService.saveProduct(newProduct);

      expect(mockApiClient.post).toHaveBeenCalledWith('/items/products', newProduct);
      expect(result).toEqual(savedProduct);
    });

    it('should update existing product', async () => {
      const existingProduct = generateMockProduct({ id: 'product-1' });
      mockApiClient.patch.mockResolvedValue({ data: existingProduct });

      const result = await apiService.saveProduct(existingProduct);

      expect(mockApiClient.patch).toHaveBeenCalledWith(
        '/items/products/product-1',
        existingProduct,
      );
      expect(result).toEqual(existingProduct);
    });

    it('should validate product data', () => {
      const validProduct = generateMockProduct();
      const invalidProduct = { vendor_id: null, name: '', category: null };

      const validResult = apiService.validateProduct(validProduct);
      const invalidResult = apiService.validateProduct(invalidProduct);

      expect(validResult.valid).toBe(true);
      expect(validResult.errors).toHaveLength(0);

      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.errors).toContain('Product name is required');
      expect(invalidResult.errors).toContain('Vendor ID is required');
      expect(invalidResult.errors).toContain('Product category is required');
    });
  });

  describe('Material Operations', () => {
    it('should fetch materials for a vendor', async () => {
      const mockMaterials = [generateMockMaterial(), generateMockMaterial({ id: 'material-2' })];
      mockApiClient.get.mockResolvedValue({ data: mockMaterials });

      const result = await apiService.getMaterials('vendor-1');

      expect(mockApiClient.get).toHaveBeenCalledWith(
        '/items/materials?filter[vendor_id][_eq]=vendor-1',
      );
      expect(result).toEqual(mockMaterials);
    });

    it('should save new material', async () => {
      const newMaterial = generateMockMaterial({ id: null });
      const savedMaterial = generateMockMaterial();
      mockApiClient.post.mockResolvedValue({ data: savedMaterial });

      const result = await apiService.saveMaterial(newMaterial);

      expect(mockApiClient.post).toHaveBeenCalledWith('/items/materials', newMaterial);
      expect(result).toEqual(savedMaterial);
    });

    it('should validate material properties', () => {
      const validMaterial = generateMockMaterial();
      const invalidMaterial = {
        name: '',
        vendor_id: null,
        type: null,
        properties: {
          roughness: 2.0, // Invalid: > 1
          metallic: -0.5, // Invalid: < 0
        },
      };

      const validResult = apiService.validateMaterial(validMaterial);
      const invalidResult = apiService.validateMaterial(invalidMaterial);

      expect(validResult.valid).toBe(true);
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.errors).toContain('Material name is required');
      expect(invalidResult.errors).toContain('Roughness must be a number between 0 and 1');
      expect(invalidResult.errors).toContain('Metallic must be a number between 0 and 1');
    });
  });

  describe('Animation Operations', () => {
    it('should fetch animations for a vendor', async () => {
      const mockAnimations = [
        generateMockAnimation(),
        generateMockAnimation({ id: 'animation-2' }),
      ];
      mockApiClient.get.mockResolvedValue({ data: mockAnimations });

      const result = await apiService.getAnimations('vendor-1');

      expect(mockApiClient.get).toHaveBeenCalledWith(
        '/items/animations?filter[vendor_id][_eq]=vendor-1',
      );
      expect(result).toEqual(mockAnimations);
    });

    it('should validate animation data', () => {
      const validAnimation = generateMockAnimation();
      const invalidAnimation = {
        name: '',
        vendor_id: null,
        target_object: null,
        duration: -1,
        keyframes: [],
      };

      const validResult = apiService.validateAnimation(validAnimation);
      const invalidResult = apiService.validateAnimation(invalidAnimation);

      expect(validResult.valid).toBe(true);
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.errors).toContain('Animation name is required');
      expect(invalidResult.errors).toContain('Target object is required');
      expect(invalidResult.errors).toContain('Duration must be a positive number');
      expect(invalidResult.errors).toContain('At least one keyframe is required');
    });
  });

  describe('File Upload Operations', () => {
    it('should upload file', async () => {
      const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
      const mockResponse = { id: 'file-1', filename_download: 'test.jpg' };
      mockApiClient.post.mockResolvedValue({ data: mockResponse });

      const result = await apiService.uploadFile(mockFile);

      expect(mockApiClient.post).toHaveBeenCalledWith('/files', expect.any(FormData));
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      mockApiClient.get.mockRejectedValue(new Error('Network error'));

      await expect(apiService.getProducts('vendor-1')).rejects.toThrow('Network error');
    });

    it('should handle validation errors', () => {
      const invalidProduct = { name: '', vendor_id: null };
      const result = apiService.validateProduct(invalidProduct);

      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });
});
