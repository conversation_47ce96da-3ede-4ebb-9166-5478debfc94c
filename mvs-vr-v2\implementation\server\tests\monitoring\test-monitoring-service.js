/**
 * Test Monitoring Service
 * 
 * Monitors test execution and results:
 * - Real-time test result tracking
 * - Performance regression detection
 * - Test failure alerting
 * - Coverage monitoring
 * - CI/CD integration metrics
 */

const EventEmitter = require('events');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

class TestMonitoringService extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.config = {
      prometheusUrl: options.prometheusUrl || 'http://localhost:9090',
      grafanaUrl: options.grafanaUrl || 'http://localhost:3001',
      slackWebhook: options.slackWebhook || process.env.SLACK_WEBHOOK_URL,
      emailService: options.emailService || null,
      thresholds: {
        coverageThreshold: 70,
        performanceRegressionThreshold: 20, // 20% degradation
        failureRateThreshold: 5, // 5% failure rate
        ...options.thresholds,
      },
      ...options,
    };
    
    this.metrics = {
      testRuns: new Map(),
      performanceBaselines: new Map(),
      coverageHistory: [],
      failurePatterns: new Map(),
    };
    
    this.alerts = {
      active: new Map(),
      history: [],
    };
    
    this.setupEventHandlers();
  }

  setupEventHandlers() {
    this.on('testStart', this.handleTestStart.bind(this));
    this.on('testComplete', this.handleTestComplete.bind(this));
    this.on('testFailure', this.handleTestFailure.bind(this));
    this.on('coverageReport', this.handleCoverageReport.bind(this));
    this.on('performanceReport', this.handlePerformanceReport.bind(this));
  }

  // Test execution monitoring
  async startMonitoring(testSuite) {
    console.log(`Starting monitoring for test suite: ${testSuite}`);
    
    const runId = this.generateRunId();
    const testRun = {
      id: runId,
      suite: testSuite,
      startTime: Date.now(),
      status: 'running',
      tests: new Map(),
      metrics: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        duration: 0,
      },
    };
    
    this.metrics.testRuns.set(runId, testRun);
    this.emit('testStart', testRun);
    
    return runId;
  }

  async recordTestResult(runId, testName, result) {
    const testRun = this.metrics.testRuns.get(runId);
    if (!testRun) return;
    
    testRun.tests.set(testName, {
      name: testName,
      status: result.status,
      duration: result.duration,
      error: result.error,
      timestamp: Date.now(),
    });
    
    // Update metrics
    testRun.metrics.total++;
    testRun.metrics[result.status]++;
    testRun.metrics.duration += result.duration;
    
    // Check for immediate alerts
    if (result.status === 'failed') {
      this.emit('testFailure', { runId, testName, result });
    }
    
    // Send metrics to Prometheus
    await this.sendMetricsToPrometheus(runId, testName, result);
  }

  async completeTestRun(runId) {
    const testRun = this.metrics.testRuns.get(runId);
    if (!testRun) return;
    
    testRun.endTime = Date.now();
    testRun.status = 'completed';
    testRun.totalDuration = testRun.endTime - testRun.startTime;
    
    this.emit('testComplete', testRun);
    
    // Analyze results
    await this.analyzeTestResults(testRun);
    
    // Generate report
    await this.generateTestReport(testRun);
    
    return testRun;
  }

  // Performance monitoring
  async handlePerformanceReport(data) {
    const { testSuite, metrics } = data;
    
    // Store current metrics
    const currentBaseline = this.metrics.performanceBaselines.get(testSuite);
    
    if (currentBaseline) {
      // Check for performance regression
      const regression = this.detectPerformanceRegression(currentBaseline, metrics);
      
      if (regression.detected) {
        await this.triggerAlert('performance_regression', {
          testSuite,
          regression,
          current: metrics,
          baseline: currentBaseline,
        });
      }
    }
    
    // Update baseline
    this.metrics.performanceBaselines.set(testSuite, {
      ...metrics,
      timestamp: Date.now(),
    });
  }

  detectPerformanceRegression(baseline, current) {
    const regressions = [];
    
    // Check response time regression
    if (current.responseTime && baseline.responseTime) {
      const increase = ((current.responseTime - baseline.responseTime) / baseline.responseTime) * 100;
      if (increase > this.config.thresholds.performanceRegressionThreshold) {
        regressions.push({
          metric: 'responseTime',
          increase: increase.toFixed(2),
          current: current.responseTime,
          baseline: baseline.responseTime,
        });
      }
    }
    
    // Check memory usage regression
    if (current.memoryUsage && baseline.memoryUsage) {
      const increase = ((current.memoryUsage - baseline.memoryUsage) / baseline.memoryUsage) * 100;
      if (increase > this.config.thresholds.performanceRegressionThreshold) {
        regressions.push({
          metric: 'memoryUsage',
          increase: increase.toFixed(2),
          current: current.memoryUsage,
          baseline: baseline.memoryUsage,
        });
      }
    }
    
    return {
      detected: regressions.length > 0,
      regressions,
    };
  }

  // Coverage monitoring
  async handleCoverageReport(data) {
    const { coverage, timestamp = Date.now() } = data;
    
    this.metrics.coverageHistory.push({
      coverage,
      timestamp,
    });
    
    // Check coverage threshold
    if (coverage.total < this.config.thresholds.coverageThreshold) {
      await this.triggerAlert('low_coverage', {
        current: coverage.total,
        threshold: this.config.thresholds.coverageThreshold,
        timestamp,
      });
    }
    
    // Send coverage metrics to Prometheus
    await this.sendCoverageMetrics(coverage);
  }

  // Alert management
  async triggerAlert(type, data) {
    const alertId = `${type}_${Date.now()}`;
    const alert = {
      id: alertId,
      type,
      data,
      timestamp: Date.now(),
      status: 'active',
    };
    
    this.alerts.active.set(alertId, alert);
    this.alerts.history.push(alert);
    
    // Send notifications
    await this.sendNotifications(alert);
    
    return alertId;
  }

  async sendNotifications(alert) {
    const message = this.formatAlertMessage(alert);
    
    // Send to Slack
    if (this.config.slackWebhook) {
      try {
        await axios.post(this.config.slackWebhook, {
          text: message,
          channel: '#test-alerts',
          username: 'Test Monitor',
          icon_emoji: ':warning:',
        });
      } catch (error) {
        console.error('Failed to send Slack notification:', error.message);
      }
    }
    
    // Send email (if configured)
    if (this.config.emailService) {
      try {
        await this.config.emailService.send({
          to: '<EMAIL>',
          subject: `Test Alert: ${alert.type}`,
          text: message,
        });
      } catch (error) {
        console.error('Failed to send email notification:', error.message);
      }
    }
  }

  formatAlertMessage(alert) {
    switch (alert.type) {
      case 'performance_regression':
        return `🚨 Performance Regression Detected in ${alert.data.testSuite}\n` +
               `Regressions: ${alert.data.regression.regressions.map(r => 
                 `${r.metric}: +${r.increase}% (${r.baseline} → ${r.current})`
               ).join(', ')}`;
      
      case 'low_coverage':
        return `📊 Low Test Coverage Alert\n` +
               `Current: ${alert.data.current}% (Threshold: ${alert.data.threshold}%)`;
      
      case 'high_failure_rate':
        return `❌ High Test Failure Rate\n` +
               `Failure Rate: ${alert.data.rate}% (Threshold: ${alert.data.threshold}%)`;
      
      default:
        return `Alert: ${alert.type}\nData: ${JSON.stringify(alert.data, null, 2)}`;
    }
  }

  // Metrics export
  async sendMetricsToPrometheus(runId, testName, result) {
    const metrics = [
      `test_duration_seconds{run_id="${runId}",test_name="${testName}",status="${result.status}"} ${result.duration / 1000}`,
      `test_result{run_id="${runId}",test_name="${testName}",status="${result.status}"} ${result.status === 'passed' ? 1 : 0}`,
    ];
    
    try {
      await axios.post(`${this.config.prometheusUrl}/api/v1/write`, metrics.join('\n'), {
        headers: { 'Content-Type': 'text/plain' },
      });
    } catch (error) {
      console.error('Failed to send metrics to Prometheus:', error.message);
    }
  }

  async sendCoverageMetrics(coverage) {
    const metrics = [
      `test_coverage_total ${coverage.total}`,
      `test_coverage_lines ${coverage.lines}`,
      `test_coverage_functions ${coverage.functions}`,
      `test_coverage_branches ${coverage.branches}`,
      `test_coverage_statements ${coverage.statements}`,
    ];
    
    try {
      await axios.post(`${this.config.prometheusUrl}/api/v1/write`, metrics.join('\n'), {
        headers: { 'Content-Type': 'text/plain' },
      });
    } catch (error) {
      console.error('Failed to send coverage metrics:', error.message);
    }
  }

  // Report generation
  async generateTestReport(testRun) {
    const report = {
      runId: testRun.id,
      suite: testRun.suite,
      summary: {
        total: testRun.metrics.total,
        passed: testRun.metrics.passed,
        failed: testRun.metrics.failed,
        skipped: testRun.metrics.skipped,
        duration: testRun.totalDuration,
        successRate: ((testRun.metrics.passed / testRun.metrics.total) * 100).toFixed(2),
      },
      tests: Array.from(testRun.tests.values()),
      timestamp: testRun.endTime,
    };
    
    // Save report to file
    const reportPath = path.join(process.cwd(), 'test-results', `${testRun.id}-report.json`);
    await fs.mkdir(path.dirname(reportPath), { recursive: true });
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`Test report saved: ${reportPath}`);
    return report;
  }

  // Analysis methods
  async analyzeTestResults(testRun) {
    const failureRate = (testRun.metrics.failed / testRun.metrics.total) * 100;
    
    if (failureRate > this.config.thresholds.failureRateThreshold) {
      await this.triggerAlert('high_failure_rate', {
        rate: failureRate.toFixed(2),
        threshold: this.config.thresholds.failureRateThreshold,
        runId: testRun.id,
        suite: testRun.suite,
      });
    }
    
    // Analyze failure patterns
    this.analyzeFailurePatterns(testRun);
  }

  analyzeFailurePatterns(testRun) {
    const failures = Array.from(testRun.tests.values()).filter(test => test.status === 'failed');
    
    failures.forEach(failure => {
      const pattern = this.extractFailurePattern(failure.error);
      const count = this.metrics.failurePatterns.get(pattern) || 0;
      this.metrics.failurePatterns.set(pattern, count + 1);
    });
  }

  extractFailurePattern(error) {
    if (!error) return 'unknown';
    
    // Extract common patterns from error messages
    if (error.includes('timeout')) return 'timeout';
    if (error.includes('connection')) return 'connection';
    if (error.includes('assertion')) return 'assertion';
    if (error.includes('network')) return 'network';
    
    return 'other';
  }

  // Utility methods
  generateRunId() {
    return `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async getMetrics() {
    return {
      testRuns: Array.from(this.metrics.testRuns.values()),
      performanceBaselines: Object.fromEntries(this.metrics.performanceBaselines),
      coverageHistory: this.metrics.coverageHistory,
      failurePatterns: Object.fromEntries(this.metrics.failurePatterns),
      activeAlerts: Array.from(this.alerts.active.values()),
    };
  }
}

module.exports = TestMonitoringService;
