/**
 * Visual Editors Integration Tests
 *
 * Tests the integration between Visual Editors components and API services
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  createMockApiClient,
  generateMockVendor,
  generateMockProduct,
  generateMockMaterial,
  generateMockAnimation,
  generateMockShowroom,
  generateMockLightingSetup,
} from './test-utils';

// Visual Editors Integration Service
class VisualEditorsIntegrationService {
  constructor(private apiClient: any) {}

  // Cross-component data flow tests
  async loadVendorData(vendorId: string) {
    const [vendor, products, materials, showrooms, animations] = await Promise.all([
      this.apiClient.get(`/items/vendors/${vendorId}`),
      this.apiClient.get(`/items/products?filter[vendor_id][_eq]=${vendorId}`),
      this.apiClient.get(`/items/materials?filter[vendor_id][_eq]=${vendorId}`),
      this.apiClient.get(`/items/showroom_layouts?filter[vendor_id][_eq]=${vendorId}`),
      this.apiClient.get(`/items/animations?filter[vendor_id][_eq]=${vendorId}`),
    ]);

    return {
      vendor: vendor.data,
      products: products.data,
      materials: materials.data,
      showrooms: showrooms.data,
      animations: animations.data,
    };
  }

  // Component state synchronization
  async syncComponentStates(vendorId: string, changes: any) {
    const updates = [];

    if (changes.layout) {
      updates.push(
        this.apiClient.patch(`/items/showroom_layouts/${changes.layout.id}`, changes.layout),
      );
    }

    if (changes.product) {
      updates.push(this.apiClient.patch(`/items/products/${changes.product.id}`, changes.product));
    }

    if (changes.material) {
      updates.push(
        this.apiClient.patch(`/items/materials/${changes.material.id}`, changes.material),
      );
    }

    if (changes.lighting) {
      updates.push(
        this.apiClient.patch(`/items/showroom_lighting/${changes.lighting.id}`, changes.lighting),
      );
    }

    if (changes.animation) {
      updates.push(
        this.apiClient.patch(`/items/animations/${changes.animation.id}`, changes.animation),
      );
    }

    return Promise.all(updates);
  }

  // Auto-save functionality
  async autoSave(vendorId: string, componentType: string, data: any) {
    const timestamp = new Date().toISOString();

    // Save to temporary storage first
    const tempSave = await this.apiClient.post('/items/temp_saves', {
      vendor_id: vendorId,
      component_type: componentType,
      data: data,
      timestamp: timestamp,
    });

    // Then save to actual collection
    let endpoint = '';
    switch (componentType) {
      case 'layout':
        endpoint = '/items/showroom_layouts';
        break;
      case 'product':
        endpoint = '/items/products';
        break;
      case 'material':
        endpoint = '/items/materials';
        break;
      case 'lighting':
        endpoint = '/items/showroom_lighting';
        break;
      case 'animation':
        endpoint = '/items/animations';
        break;
    }

    if (data.id) {
      return this.apiClient.patch(`${endpoint}/${data.id}`, data);
    } else {
      return this.apiClient.post(endpoint, { ...data, vendor_id: vendorId });
    }
  }

  // Validation across components
  validateCrossComponentData(data: any): { valid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if materials used in products exist
    if (data.products && data.materials) {
      data.products.forEach((product: any) => {
        if (product.material_ids) {
          product.material_ids.forEach((materialId: string) => {
            const materialExists = data.materials.some((m: any) => m.id === materialId);
            if (!materialExists) {
              errors.push(
                `Product "${product.name}" references non-existent material: ${materialId}`,
              );
            }
          });
        }
      });
    }

    // Check if products used in layouts exist
    if (data.showrooms && data.products) {
      data.showrooms.forEach((showroom: any) => {
        if (showroom.items) {
          showroom.items.forEach((item: any) => {
            const productExists = data.products.some((p: any) => p.id === item.productId);
            if (!productExists) {
              errors.push(
                `Showroom "${showroom.name}" references non-existent product: ${item.productId}`,
              );
            }
          });
        }
      });
    }

    // Check if animations target existing objects
    if (data.animations && data.products) {
      data.animations.forEach((animation: any) => {
        if (animation.target_object) {
          const targetExists = data.products.some((p: any) => p.id === animation.target_object);
          if (!targetExists) {
            warnings.push(
              `Animation "${animation.name}" targets non-existent object: ${animation.target_object}`,
            );
          }
        }
      });
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // Performance optimization
  async optimizeAssetLoading(vendorId: string) {
    // Get all assets for vendor
    const assets = await this.apiClient.get(`/items/assets?filter[vendor_id][_eq]=${vendorId}`);

    // Group by type and priority
    const assetGroups = {
      critical: assets.data.filter((a: any) => a.priority === 'critical'),
      high: assets.data.filter((a: any) => a.priority === 'high'),
      normal: assets.data.filter((a: any) => a.priority === 'normal'),
      low: assets.data.filter((a: any) => a.priority === 'low'),
    };

    // Return loading strategy
    return {
      preload: assetGroups.critical,
      loadOnDemand: [...assetGroups.high, ...assetGroups.normal],
      lazy: assetGroups.low,
      totalSize: assets.data.reduce((sum: number, asset: any) => sum + (asset.size || 0), 0),
    };
  }
}

describe('Visual Editors Integration Tests', () => {
  let integrationService: VisualEditorsIntegrationService;
  let mockApiClient: any;

  beforeEach(() => {
    mockApiClient = createMockApiClient();
    integrationService = new VisualEditorsIntegrationService(mockApiClient);
    vi.clearAllMocks();
  });

  describe('Cross-Component Data Loading', () => {
    it('should load all vendor data in parallel', async () => {
      const mockVendor = generateMockVendor();
      const mockProducts = [generateMockProduct(), generateMockProduct({ id: 'product-2' })];
      const mockMaterials = [generateMockMaterial(), generateMockMaterial({ id: 'material-2' })];
      const mockShowrooms = [generateMockShowroom()];
      const mockAnimations = [generateMockAnimation()];

      mockApiClient.get.mockImplementation((url: string) => {
        if (url.includes('/items/vendors/')) return Promise.resolve({ data: mockVendor });
        if (url.includes('/items/products')) return Promise.resolve({ data: mockProducts });
        if (url.includes('/items/materials')) return Promise.resolve({ data: mockMaterials });
        if (url.includes('/items/showroom_layouts'))
          return Promise.resolve({ data: mockShowrooms });
        if (url.includes('/items/animations')) return Promise.resolve({ data: mockAnimations });
        return Promise.resolve({ data: [] });
      });

      const result = await integrationService.loadVendorData('vendor-1');

      expect(result.vendor).toEqual(mockVendor);
      expect(result.products).toEqual(mockProducts);
      expect(result.materials).toEqual(mockMaterials);
      expect(result.showrooms).toEqual(mockShowrooms);
      expect(result.animations).toEqual(mockAnimations);
      expect(mockApiClient.get).toHaveBeenCalledTimes(5);
    });

    it('should handle partial data loading failures', async () => {
      mockApiClient.get.mockImplementation((url: string) => {
        if (url.includes('/items/vendors/')) return Promise.resolve({ data: generateMockVendor() });
        if (url.includes('/items/products')) return Promise.reject(new Error('Products API error'));
        return Promise.resolve({ data: [] });
      });

      await expect(integrationService.loadVendorData('vendor-1')).rejects.toThrow(
        'Products API error',
      );
    });
  });

  describe('Component State Synchronization', () => {
    it('should sync multiple component changes', async () => {
      const changes = {
        layout: { id: 'layout-1', name: 'Updated Layout' },
        product: { id: 'product-1', name: 'Updated Product' },
        material: { id: 'material-1', name: 'Updated Material' },
      };

      mockApiClient.patch.mockResolvedValue({ data: { success: true } });

      await integrationService.syncComponentStates('vendor-1', changes);

      expect(mockApiClient.patch).toHaveBeenCalledTimes(3);
      expect(mockApiClient.patch).toHaveBeenCalledWith(
        '/items/showroom_layouts/layout-1',
        changes.layout,
      );
      expect(mockApiClient.patch).toHaveBeenCalledWith(
        '/items/products/product-1',
        changes.product,
      );
      expect(mockApiClient.patch).toHaveBeenCalledWith(
        '/items/materials/material-1',
        changes.material,
      );
    });

    it('should handle sync failures gracefully', async () => {
      const changes = {
        layout: { id: 'layout-1', name: 'Updated Layout' },
        product: { id: 'product-1', name: 'Updated Product' },
      };

      mockApiClient.patch.mockImplementation((url: string) => {
        if (url.includes('showroom_layouts')) return Promise.resolve({ data: { success: true } });
        if (url.includes('products')) return Promise.reject(new Error('Product sync failed'));
        return Promise.resolve({ data: { success: true } });
      });

      await expect(integrationService.syncComponentStates('vendor-1', changes)).rejects.toThrow();
    });
  });

  describe('Auto-Save Functionality', () => {
    it('should auto-save new data', async () => {
      const newProduct = generateMockProduct({ id: null });

      mockApiClient.post.mockResolvedValue({ data: { success: true } });

      await integrationService.autoSave('vendor-1', 'product', newProduct);

      expect(mockApiClient.post).toHaveBeenCalledWith('/items/temp_saves', expect.any(Object));
      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/items/products',
        expect.objectContaining({
          ...newProduct,
          vendor_id: 'vendor-1',
        }),
      );
    });

    it('should auto-save existing data', async () => {
      const existingProduct = generateMockProduct({ id: 'product-1' });

      mockApiClient.post.mockResolvedValue({ data: { success: true } });
      mockApiClient.patch.mockResolvedValue({ data: { success: true } });

      await integrationService.autoSave('vendor-1', 'product', existingProduct);

      expect(mockApiClient.patch).toHaveBeenCalledWith(
        '/items/products/product-1',
        existingProduct,
      );
    });
  });

  describe('Cross-Component Validation', () => {
    it('should validate material references in products', () => {
      const data = {
        products: [
          { id: 'product-1', name: 'Product 1', material_ids: ['material-1', 'material-2'] },
        ],
        materials: [
          { id: 'material-1', name: 'Material 1' },
          // material-2 is missing
        ],
      };

      const result = integrationService.validateCrossComponentData(data);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain(
        'Product "Product 1" references non-existent material: material-2',
      );
    });

    it('should validate product references in showrooms', () => {
      const data = {
        showrooms: [
          {
            id: 'showroom-1',
            name: 'Showroom 1',
            items: [{ productId: 'product-1' }, { productId: 'product-2' }],
          },
        ],
        products: [
          { id: 'product-1', name: 'Product 1' },
          // product-2 is missing
        ],
      };

      const result = integrationService.validateCrossComponentData(data);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain(
        'Showroom "Showroom 1" references non-existent product: product-2',
      );
    });

    it('should validate animation targets', () => {
      const data = {
        animations: [{ id: 'animation-1', name: 'Animation 1', target_object: 'product-1' }],
        products: [
          { id: 'product-2', name: 'Product 2' },
          // product-1 is missing
        ],
      };

      const result = integrationService.validateCrossComponentData(data);

      expect(result.valid).toBe(true); // Warnings don't make it invalid
      expect(result.warnings).toContain(
        'Animation "Animation 1" targets non-existent object: product-1',
      );
    });
  });

  describe('Performance Optimization', () => {
    it('should optimize asset loading strategy', async () => {
      const mockAssets = [
        { id: 'asset-1', priority: 'critical', size: 1000 },
        { id: 'asset-2', priority: 'high', size: 2000 },
        { id: 'asset-3', priority: 'normal', size: 3000 },
        { id: 'asset-4', priority: 'low', size: 4000 },
      ];

      mockApiClient.get.mockResolvedValue({ data: mockAssets });

      const result = await integrationService.optimizeAssetLoading('vendor-1');

      expect(result.preload).toHaveLength(1);
      expect(result.loadOnDemand).toHaveLength(2);
      expect(result.lazy).toHaveLength(1);
      expect(result.totalSize).toBe(10000);
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors', async () => {
      mockApiClient.get.mockRejectedValue(new Error('Network error'));

      await expect(integrationService.loadVendorData('vendor-1')).rejects.toThrow('Network error');
    });

    it('should handle validation errors', () => {
      const invalidData = {
        products: [{ id: 'product-1', material_ids: ['missing-material'] }],
        materials: [],
      };

      const result = integrationService.validateCrossComponentData(invalidData);

      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });
});
