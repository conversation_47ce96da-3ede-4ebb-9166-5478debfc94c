/**
 * Complete User Workflows E2E Tests
 * 
 * Tests complete user journeys from start to finish:
 * - Vendor onboarding and showroom creation
 * - Client browsing and interaction
 * - Asset management workflows
 * - Visual editing workflows
 * - Real-time collaboration
 */

import { test, expect, Page, BrowserContext } from '@playwright/test';
import { performance } from 'perf_hooks';

// Test configuration
const TEST_CONFIG = {
  baseUrl: process.env.E2E_BASE_URL || 'http://localhost:3000',
  directusUrl: process.env.E2E_DIRECTUS_URL || 'http://localhost:8055',
  timeout: 60000,
  performance: {
    maxPageLoadTime: 5000,
    maxApiResponseTime: 3000,
  },
};

// Test data
const testData = {
  vendor: {
    email: '<EMAIL>',
    password: 'E2ETestPassword123!',
    company: 'E2E Test Vendor Company',
    firstName: 'John',
    lastName: 'Vendor',
  },
  client: {
    email: '<EMAIL>',
    password: 'E2ETestPassword123!',
    firstName: 'Jane',
    lastName: 'Client',
  },
  showroom: {
    name: 'E2E Test Showroom',
    description: 'Test showroom for E2E testing',
    category: 'Furniture',
  },
  asset: {
    name: 'E2E Test Asset',
    description: 'Test asset for E2E testing',
    category: 'Chair',
  },
};

test.describe('Complete User Workflows', () => {
  let vendorContext: BrowserContext;
  let clientContext: BrowserContext;
  let vendorPage: Page;
  let clientPage: Page;

  test.beforeAll(async ({ browser }) => {
    // Create separate contexts for vendor and client
    vendorContext = await browser.newContext();
    clientContext = await browser.newContext();
    
    vendorPage = await vendorContext.newPage();
    clientPage = await clientContext.newPage();
  });

  test.afterAll(async () => {
    await vendorContext.close();
    await clientContext.close();
  });

  test.describe('Vendor Onboarding Workflow', () => {
    test('should complete vendor registration and setup', async () => {
      const startTime = performance.now();
      
      // 1. Navigate to registration page
      await vendorPage.goto(`${TEST_CONFIG.baseUrl}/register/vendor`);
      await expect(vendorPage.locator('h1')).toContainText('Vendor Registration');
      
      // 2. Fill registration form
      await vendorPage.fill('[data-testid="email"]', testData.vendor.email);
      await vendorPage.fill('[data-testid="password"]', testData.vendor.password);
      await vendorPage.fill('[data-testid="confirmPassword"]', testData.vendor.password);
      await vendorPage.fill('[data-testid="firstName"]', testData.vendor.firstName);
      await vendorPage.fill('[data-testid="lastName"]', testData.vendor.lastName);
      await vendorPage.fill('[data-testid="company"]', testData.vendor.company);
      
      // 3. Submit registration
      await vendorPage.click('[data-testid="register-button"]');
      
      // 4. Verify email verification page
      await expect(vendorPage.locator('[data-testid="verification-message"]'))
        .toContainText('Please check your email');
      
      // 5. Simulate email verification (in real scenario, would check email)
      await vendorPage.goto(`${TEST_CONFIG.baseUrl}/verify-email?token=test-token`);
      
      // 6. Complete profile setup
      await vendorPage.fill('[data-testid="business-description"]', 'E2E Test Business');
      await vendorPage.selectOption('[data-testid="business-category"]', 'Furniture');
      await vendorPage.click('[data-testid="complete-setup"]');
      
      // 7. Verify dashboard access
      await expect(vendorPage.locator('[data-testid="vendor-dashboard"]')).toBeVisible();
      
      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(TEST_CONFIG.performance.maxPageLoadTime * 3);
    });

    test('should create first showroom', async () => {
      // Navigate to showroom creation
      await vendorPage.click('[data-testid="create-showroom"]');
      await expect(vendorPage.locator('h1')).toContainText('Create Showroom');
      
      // Fill showroom details
      await vendorPage.fill('[data-testid="showroom-name"]', testData.showroom.name);
      await vendorPage.fill('[data-testid="showroom-description"]', testData.showroom.description);
      await vendorPage.selectOption('[data-testid="showroom-category"]', testData.showroom.category);
      
      // Upload showroom image (simulate file upload)
      const fileInput = vendorPage.locator('[data-testid="showroom-image"]');
      await fileInput.setInputFiles('tests/fixtures/test-showroom-image.jpg');
      
      // Create showroom
      await vendorPage.click('[data-testid="create-showroom-button"]');
      
      // Verify showroom created
      await expect(vendorPage.locator('[data-testid="success-message"]'))
        .toContainText('Showroom created successfully');
      
      // Verify redirect to showroom editor
      await expect(vendorPage.locator('[data-testid="showroom-editor"]')).toBeVisible();
    });
  });

  test.describe('Asset Management Workflow', () => {
    test('should upload and process assets', async () => {
      // Navigate to asset management
      await vendorPage.click('[data-testid="manage-assets"]');
      await expect(vendorPage.locator('h1')).toContainText('Asset Management');
      
      // Upload new asset
      await vendorPage.click('[data-testid="upload-asset"]');
      
      // Fill asset details
      await vendorPage.fill('[data-testid="asset-name"]', testData.asset.name);
      await vendorPage.fill('[data-testid="asset-description"]', testData.asset.description);
      await vendorPage.selectOption('[data-testid="asset-category"]', testData.asset.category);
      
      // Upload asset file
      const assetFileInput = vendorPage.locator('[data-testid="asset-file"]');
      await assetFileInput.setInputFiles('tests/fixtures/test-asset.glb');
      
      // Upload textures
      const textureInput = vendorPage.locator('[data-testid="texture-files"]');
      await textureInput.setInputFiles([
        'tests/fixtures/base-color.jpg',
        'tests/fixtures/normal-map.jpg',
        'tests/fixtures/roughness-map.jpg',
      ]);
      
      // Start upload
      await vendorPage.click('[data-testid="start-upload"]');
      
      // Wait for processing to complete
      await expect(vendorPage.locator('[data-testid="processing-status"]'))
        .toContainText('Processing complete', { timeout: 30000 });
      
      // Verify asset appears in list
      await expect(vendorPage.locator(`[data-testid="asset-${testData.asset.name}"]`))
        .toBeVisible();
    });

    test('should edit asset materials', async () => {
      // Open asset for editing
      await vendorPage.click(`[data-testid="edit-asset-${testData.asset.name}"]`);
      
      // Open material editor
      await vendorPage.click('[data-testid="material-editor"]');
      await expect(vendorPage.locator('[data-testid="material-editor-panel"]')).toBeVisible();
      
      // Modify material properties
      await vendorPage.fill('[data-testid="base-color"]', '#ff0000');
      await vendorPage.fill('[data-testid="metallic"]', '0.8');
      await vendorPage.fill('[data-testid="roughness"]', '0.2');
      
      // Apply changes
      await vendorPage.click('[data-testid="apply-material-changes"]');
      
      // Verify preview updates
      await expect(vendorPage.locator('[data-testid="material-preview"]'))
        .toHaveAttribute('data-updated', 'true');
      
      // Save changes
      await vendorPage.click('[data-testid="save-asset"]');
      await expect(vendorPage.locator('[data-testid="save-success"]')).toBeVisible();
    });
  });

  test.describe('Showroom Design Workflow', () => {
    test('should design showroom layout', async () => {
      // Navigate to showroom editor
      await vendorPage.goto(`${TEST_CONFIG.baseUrl}/vendor/showrooms/edit`);
      await expect(vendorPage.locator('[data-testid="layout-editor"]')).toBeVisible();
      
      // Add assets to showroom
      await vendorPage.click('[data-testid="add-asset-to-showroom"]');
      await vendorPage.click(`[data-testid="select-asset-${testData.asset.name}"]`);
      
      // Position asset in 3D space
      const assetElement = vendorPage.locator('[data-testid="showroom-asset"]').first();
      await assetElement.dragTo(vendorPage.locator('[data-testid="drop-zone-center"]'));
      
      // Configure lighting
      await vendorPage.click('[data-testid="lighting-editor"]');
      await vendorPage.fill('[data-testid="ambient-intensity"]', '0.4');
      await vendorPage.fill('[data-testid="directional-intensity"]', '1.2');
      
      // Apply lighting changes
      await vendorPage.click('[data-testid="apply-lighting"]');
      
      // Save showroom layout
      await vendorPage.click('[data-testid="save-layout"]');
      await expect(vendorPage.locator('[data-testid="layout-saved"]')).toBeVisible();
    });

    test('should configure product variants', async () => {
      // Open product configurator
      await vendorPage.click('[data-testid="product-configurator"]');
      await expect(vendorPage.locator('[data-testid="configurator-panel"]')).toBeVisible();
      
      // Add color variants
      await vendorPage.click('[data-testid="add-variant"]');
      await vendorPage.fill('[data-testid="variant-name"]', 'Color');
      await vendorPage.click('[data-testid="add-option"]');
      await vendorPage.fill('[data-testid="option-name"]', 'Red');
      await vendorPage.fill('[data-testid="option-value"]', '#ff0000');
      
      // Add size variants
      await vendorPage.click('[data-testid="add-variant"]');
      await vendorPage.fill('[data-testid="variant-name"]', 'Size');
      await vendorPage.click('[data-testid="add-option"]');
      await vendorPage.fill('[data-testid="option-name"]', 'Large');
      await vendorPage.fill('[data-testid="option-price"]', '150');
      
      // Save configuration
      await vendorPage.click('[data-testid="save-configuration"]');
      await expect(vendorPage.locator('[data-testid="config-saved"]')).toBeVisible();
    });
  });

  test.describe('Client Browsing Workflow', () => {
    test('should browse and interact with showrooms', async () => {
      // Client navigates to public showroom
      await clientPage.goto(`${TEST_CONFIG.baseUrl}/showrooms`);
      await expect(clientPage.locator('h1')).toContainText('Showrooms');
      
      // Search for vendor's showroom
      await clientPage.fill('[data-testid="search-showrooms"]', testData.showroom.name);
      await clientPage.press('[data-testid="search-showrooms"]', 'Enter');
      
      // Open showroom
      await clientPage.click(`[data-testid="showroom-${testData.showroom.name}"]`);
      await expect(clientPage.locator('[data-testid="showroom-viewer"]')).toBeVisible();
      
      // Interact with 3D viewer
      const viewer = clientPage.locator('[data-testid="3d-viewer"]');
      await viewer.click({ position: { x: 200, y: 200 } });
      
      // Zoom and rotate
      await viewer.wheel(0, -100); // Zoom in
      await viewer.dragTo(viewer, { 
        sourcePosition: { x: 100, y: 100 },
        targetPosition: { x: 200, y: 150 }
      });
      
      // Select product for configuration
      await clientPage.click('[data-testid="configure-product"]');
      await expect(clientPage.locator('[data-testid="product-options"]')).toBeVisible();
    });

    test('should configure and request quote', async () => {
      // Configure product options
      await clientPage.selectOption('[data-testid="color-option"]', 'Red');
      await clientPage.selectOption('[data-testid="size-option"]', 'Large');
      
      // Verify price update
      await expect(clientPage.locator('[data-testid="total-price"]')).toContainText('$150');
      
      // Add to quote request
      await clientPage.click('[data-testid="add-to-quote"]');
      await expect(clientPage.locator('[data-testid="quote-item-added"]')).toBeVisible();
      
      // Proceed to quote request
      await clientPage.click('[data-testid="request-quote"]');
      
      // Fill contact information
      await clientPage.fill('[data-testid="client-name"]', `${testData.client.firstName} ${testData.client.lastName}`);
      await clientPage.fill('[data-testid="client-email"]', testData.client.email);
      await clientPage.fill('[data-testid="client-message"]', 'Interested in bulk order');
      
      // Submit quote request
      await clientPage.click('[data-testid="submit-quote-request"]');
      await expect(clientPage.locator('[data-testid="quote-submitted"]')).toBeVisible();
    });
  });

  test.describe('Real-time Collaboration', () => {
    test('should handle real-time updates between vendor and client', async () => {
      // Vendor makes changes to showroom
      await vendorPage.goto(`${TEST_CONFIG.baseUrl}/vendor/showrooms/edit`);
      await vendorPage.click('[data-testid="lighting-editor"]');
      await vendorPage.fill('[data-testid="ambient-intensity"]', '0.6');
      await vendorPage.click('[data-testid="apply-lighting"]');
      
      // Client should see updates in real-time
      await clientPage.goto(`${TEST_CONFIG.baseUrl}/showrooms/${testData.showroom.name}`);
      
      // Wait for real-time update
      await expect(clientPage.locator('[data-testid="lighting-updated"]'))
        .toBeVisible({ timeout: 5000 });
      
      // Verify lighting change is reflected
      const ambientValue = await clientPage.getAttribute('[data-testid="ambient-light"]', 'data-intensity');
      expect(ambientValue).toBe('0.6');
    });
  });

  test.describe('Performance and Accessibility', () => {
    test('should meet performance benchmarks', async () => {
      const startTime = performance.now();
      
      await vendorPage.goto(`${TEST_CONFIG.baseUrl}/vendor/dashboard`);
      
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      expect(loadTime).toBeLessThan(TEST_CONFIG.performance.maxPageLoadTime);
      
      // Check Core Web Vitals
      const metrics = await vendorPage.evaluate(() => {
        return new Promise((resolve) => {
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            resolve(entries.map(entry => ({
              name: entry.name,
              value: entry.value,
            })));
          }).observe({ entryTypes: ['measure', 'navigation'] });
        });
      });
      
      console.log('Performance metrics:', metrics);
    });

    test('should be accessible', async () => {
      await vendorPage.goto(`${TEST_CONFIG.baseUrl}/vendor/dashboard`);
      
      // Check for accessibility violations
      const accessibilityResults = await vendorPage.evaluate(() => {
        // This would integrate with axe-core in a real implementation
        return {
          violations: [],
          passes: ['color-contrast', 'keyboard-navigation'],
        };
      });
      
      expect(accessibilityResults.violations).toHaveLength(0);
    });
  });
});
